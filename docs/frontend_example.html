<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态表单系统示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-field {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        .required {
            color: #e74c3c;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        .checkbox-field, .radio-field {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .checkbox-field input, .radio-field input {
            width: auto;
            margin-right: 8px;
        }
        .multi-select {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 150px;
            overflow-y: auto;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-secondary {
            background: #95a5a6;
        }
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        .entity-selector {
            margin-bottom: 30px;
            padding: 20px;
            background: #ecf0f1;
            border-radius: 4px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            color: #27ae60;
            background: #f0f9f0;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>动态表单系统示例</h1>
        
        <div class="entity-selector">
            <label for="entityType">选择实体类型：</label>
            <select id="entityType" onchange="loadForm()">
                <option value="">请选择实体类型</option>
                <option value="product">产品 (Product)</option>
                <option value="article">文章 (Article)</option>
                <option value="user_profile">用户资料 (User Profile)</option>
                <option value="event">活动 (Event)</option>
            </select>
        </div>

        <div id="message"></div>
        <div id="formContainer"></div>
    </div>

    <script>
        const API_BASE = '/api'; // 根据实际API地址调整
        let currentFormSchema = [];

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }

        // 加载表单
        async function loadForm() {
            const entityType = document.getElementById('entityType').value;
            const formContainer = document.getElementById('formContainer');
            
            if (!entityType) {
                formContainer.innerHTML = '';
                return;
            }

            formContainer.innerHTML = '<div class="loading">正在加载表单...</div>';

            try {
                const response = await fetch(`${API_BASE}/dynamic-entities/form?entity_type=${entityType}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    currentFormSchema = result.data.form_schema;
                    renderForm(currentFormSchema);
                } else {
                    throw new Error(result.msg || '加载表单失败');
                }
            } catch (error) {
                formContainer.innerHTML = `<div class="error">加载表单失败: ${error.message}</div>`;
            }
        }

        // 渲染表单
        function renderForm(formSchema) {
            const formContainer = document.getElementById('formContainer');
            
            let formHTML = '<form id="dynamicForm">';
            
            formSchema.forEach(field => {
                formHTML += createFieldHTML(field);
            });
            
            formHTML += `
                <div style="margin-top: 30px;">
                    <button type="button" class="btn" onclick="submitForm()">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="validateForm()">验证</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                </div>
            </form>`;
            
            formContainer.innerHTML = formHTML;
        }

        // 创建字段HTML
        function createFieldHTML(field) {
            const required = field.is_required ? ' <span class="required">*</span>' : '';
            const requiredAttr = field.is_required ? ' required' : '';
            
            let fieldHTML = `<div class="form-field">
                <label>${field.attr_name}${required}</label>`;

            switch (field.field_type) {
                case 'text':
                    fieldHTML += `<input type="text" name="${field.attr_code}" 
                        placeholder="${field.config.placeholder || ''}"
                        maxlength="${field.config.max_length || ''}"${requiredAttr}>`;
                    break;
                    
                case 'number':
                    fieldHTML += `<input type="number" name="${field.attr_code}" 
                        placeholder="${field.config.placeholder || ''}"
                        min="${field.config.min || ''}"
                        max="${field.config.max || ''}"
                        step="${field.config.step || ''}"${requiredAttr}>`;
                    break;
                    
                case 'textarea':
                    fieldHTML += `<textarea name="${field.attr_code}" 
                        placeholder="${field.config.placeholder || ''}"
                        rows="${field.config.rows || 3}"
                        maxlength="${field.config.max_length || ''}"${requiredAttr}></textarea>`;
                    break;
                    
                case 'select':
                    fieldHTML += `<select name="${field.attr_code}"${requiredAttr}>
                        <option value="">请选择</option>`;
                    if (field.config.options) {
                        field.config.options.forEach(option => {
                            fieldHTML += `<option value="${option.value}">${option.label}</option>`;
                        });
                    }
                    fieldHTML += '</select>';
                    break;
                    
                case 'multi_select':
                    fieldHTML += '<div class="multi-select">';
                    if (field.config.options) {
                        field.config.options.forEach(option => {
                            fieldHTML += `<div class="checkbox-field">
                                <input type="checkbox" name="${field.attr_code}" value="${option.value}" id="${field.attr_code}_${option.value}">
                                <label for="${field.attr_code}_${option.value}">${option.label}</label>
                            </div>`;
                        });
                    }
                    fieldHTML += '</div>';
                    break;
                    
                case 'radio':
                    if (field.config.options) {
                        field.config.options.forEach(option => {
                            fieldHTML += `<div class="radio-field">
                                <input type="radio" name="${field.attr_code}" value="${option.value}" id="${field.attr_code}_${option.value}"${requiredAttr}>
                                <label for="${field.attr_code}_${option.value}">${option.label}</label>
                            </div>`;
                        });
                    }
                    break;
                    
                case 'checkbox':
                    fieldHTML += `<div class="checkbox-field">
                        <input type="checkbox" name="${field.attr_code}" value="true" id="${field.attr_code}">
                        <label for="${field.attr_code}">${field.config.label || field.attr_name}</label>
                    </div>`;
                    break;
                    
                case 'date':
                    fieldHTML += `<input type="date" name="${field.attr_code}"${requiredAttr}>`;
                    break;
                    
                case 'datetime':
                    fieldHTML += `<input type="datetime-local" name="${field.attr_code}"${requiredAttr}>`;
                    break;
                    
                default:
                    fieldHTML += `<input type="text" name="${field.attr_code}" 
                        placeholder="${field.config.placeholder || ''}"${requiredAttr}>`;
            }
            
            fieldHTML += '</div>';
            return fieldHTML;
        }

        // 收集表单数据
        function collectFormData() {
            const form = document.getElementById('dynamicForm');
            const formData = new FormData(form);
            const data = {};

            // 处理普通字段
            for (let [key, value] of formData.entries()) {
                if (data[key]) {
                    // 多选字段
                    if (!Array.isArray(data[key])) {
                        data[key] = [data[key]];
                    }
                    data[key].push(value);
                } else {
                    data[key] = value;
                }
            }

            // 处理复选框字段
            currentFormSchema.forEach(field => {
                if (field.field_type === 'checkbox' && !data[field.attr_code]) {
                    data[field.attr_code] = false;
                } else if (field.field_type === 'checkbox' && data[field.attr_code] === 'true') {
                    data[field.attr_code] = true;
                }
            });

            return data;
        }

        // 提交表单
        async function submitForm() {
            const entityType = document.getElementById('entityType').value;
            const attributes = collectFormData();

            try {
                const response = await fetch(`${API_BASE}/dynamic-entities`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        entity_type: entityType,
                        attributes: attributes,
                        status: '0'
                    })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('保存成功！', 'success');
                    resetForm();
                } else {
                    throw new Error(result.msg || '保存失败');
                }
            } catch (error) {
                showMessage(`保存失败: ${error.message}`, 'error');
            }
        }

        // 验证表单
        async function validateForm() {
            const entityType = document.getElementById('entityType').value;
            const attributes = collectFormData();

            try {
                const response = await fetch(`${API_BASE}/dynamic-entities/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        entity_type: entityType,
                        attributes: attributes
                    })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    if (result.data.valid) {
                        showMessage('数据验证通过！', 'success');
                    } else {
                        showMessage(`验证失败: ${result.data.errors.join(', ')}`, 'error');
                    }
                } else {
                    throw new Error(result.msg || '验证失败');
                }
            } catch (error) {
                showMessage(`验证失败: ${error.message}`, 'error');
            }
        }

        // 重置表单
        function resetForm() {
            const form = document.getElementById('dynamicForm');
            if (form) {
                form.reset();
            }
        }
    </script>
</body>
</html>
