# 动态表单系统 API 文档

## 概述

动态表单系统允许你通过配置属性定义来动态生成表单，并将用户填写的数据保存到动态实体表中。

## 工作流程

1. **定义属性** - 在 `sys_attribute_definitions` 表中定义实体的属性
2. **获取表单结构** - 前端调用API获取表单结构
3. **渲染表单** - 前端根据表单结构动态渲染表单
4. **提交数据** - 用户填写表单后提交数据
5. **保存数据** - 后端验证并保存到 `sys_dynamic_entities` 表

## API 接口

### 1. 获取动态表单结构

**GET** `/dynamic-entities/form?entity_type={entity_type}`

获取指定实体类型的表单结构，用于前端动态生成表单。

**请求参数：**
- `entity_type` (string, required): 实体类型，如 "product", "article" 等

**响应示例：**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "entity_type": "product",
    "form_schema": [
      {
        "id": 1,
        "entity_type": "product",
        "attr_code": "title",
        "attr_name": "产品标题",
        "field_type": "text",
        "data_type": "string",
        "is_required": 1,
        "config": {
          "placeholder": "请输入产品标题",
          "max_length": 100
        },
        "sort_order": 1
      },
      {
        "id": 2,
        "entity_type": "product",
        "attr_code": "price",
        "attr_name": "价格",
        "field_type": "number",
        "data_type": "decimal",
        "is_required": 1,
        "config": {
          "min": 0,
          "step": 0.01,
          "placeholder": "请输入价格"
        },
        "sort_order": 2
      }
    ]
  }
}
```

### 2. 创建动态实体

**POST** `/dynamic-entities`

根据表单数据创建动态实体。

**请求体：**
```json
{
  "entity_type": "product",
  "attributes": {
    "title": "iPhone 15 Pro",
    "price": 7999.00,
    "description": "最新款iPhone",
    "category": "手机"
  },
  "status": "0"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

### 3. 更新动态实体

**PUT** `/dynamic-entities`

更新现有的动态实体。

**请求体：**
```json
{
  "id": 1,
  "entity_type": "product",
  "attributes": {
    "title": "iPhone 15 Pro Max",
    "price": 8999.00,
    "description": "最新款iPhone Pro Max版本",
    "category": "手机"
  },
  "status": "0"
}
```

### 4. 获取动态实体列表

**GET** `/dynamic-entities/list?entity_type={entity_type}&page=1&pageSize=10`

获取动态实体列表。

**查询参数：**
- `entity_type` (string, optional): 实体类型过滤
- `status` (string, optional): 状态过滤 (0-正常, 1-停用)
- `create_by` (string, optional): 创建者过滤
- `begin_time` (string, optional): 开始时间过滤
- `end_time` (string, optional): 结束时间过滤
- `page` (int, optional): 页码，默认1
- `pageSize` (int, optional): 每页大小，默认10

**响应示例：**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "page": 1,
    "pageSize": 10,
    "total": 1,
    "rows": [
      {
        "id": 1,
        "entity_type": "product",
        "attributes": {
          "title": "iPhone 15 Pro",
          "price": 7999.00,
          "description": "最新款iPhone",
          "category": "手机"
        },
        "status": "0",
        "create_by": "admin",
        "create_time": "2024-01-01T10:00:00Z",
        "update_by": "admin",
        "update_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 5. 获取动态实体详情

**GET** `/dynamic-entities/{id}`

获取指定ID的动态实体详情。

**响应示例：**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "entity_type": "product",
    "attributes": {
      "title": "iPhone 15 Pro",
      "price": 7999.00,
      "description": "最新款iPhone",
      "category": "手机"
    },
    "status": "0",
    "create_by": "admin",
    "create_time": "2024-01-01T10:00:00Z",
    "update_by": "admin",
    "update_time": "2024-01-01T10:00:00Z"
  }
}
```

### 6. 删除动态实体

**DELETE** `/dynamic-entities/{id}`

删除指定ID的动态实体。

### 7. 批量删除动态实体

**POST** `/dynamic-entities/batch-delete`

批量删除动态实体。

**请求体：**
```json
{
  "ids": [1, 2, 3]
}
```

### 8. 验证动态实体数据

**POST** `/dynamic-entities/validate`

验证动态实体数据是否符合属性定义要求。

**请求体：**
```json
{
  "entity_type": "product",
  "attributes": {
    "title": "iPhone 15 Pro",
    "price": 7999.00
  }
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "valid": true,
    "message": "数据验证通过"
  }
}
```

## 前端集成示例

### 1. 获取表单结构并渲染表单

```javascript
// 获取表单结构
async function getFormSchema(entityType) {
  const response = await fetch(`/api/dynamic-entities/form?entity_type=${entityType}`);
  const result = await response.json();
  return result.data.form_schema;
}

// 根据表单结构渲染表单
function renderForm(formSchema) {
  const formContainer = document.getElementById('dynamic-form');
  
  formSchema.forEach(field => {
    const fieldElement = createFieldElement(field);
    formContainer.appendChild(fieldElement);
  });
}

// 创建表单字段元素
function createFieldElement(field) {
  const wrapper = document.createElement('div');
  wrapper.className = 'form-field';
  
  const label = document.createElement('label');
  label.textContent = field.attr_name;
  if (field.is_required) {
    label.textContent += ' *';
  }
  
  let input;
  switch (field.field_type) {
    case 'text':
      input = document.createElement('input');
      input.type = 'text';
      input.placeholder = field.config.placeholder || '';
      break;
    case 'number':
      input = document.createElement('input');
      input.type = 'number';
      input.min = field.config.min || '';
      input.max = field.config.max || '';
      input.step = field.config.step || '';
      break;
    case 'textarea':
      input = document.createElement('textarea');
      input.placeholder = field.config.placeholder || '';
      break;
    // 添加更多字段类型...
  }
  
  input.name = field.attr_code;
  input.required = field.is_required === 1;
  
  wrapper.appendChild(label);
  wrapper.appendChild(input);
  
  return wrapper;
}
```

### 2. 提交表单数据

```javascript
// 提交表单
async function submitForm(entityType, formData) {
  const response = await fetch('/api/dynamic-entities', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      entity_type: entityType,
      attributes: formData,
      status: '0'
    })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    alert('保存成功！');
  } else {
    alert('保存失败：' + result.msg);
  }
}
```

## 注意事项

1. **权限控制**: 所有API都需要JWT认证，部分操作需要特定权限
2. **数据验证**: 创建和更新时会自动验证数据类型和必填字段
3. **JSON存储**: 动态属性以JSON格式存储在数据库中
4. **字段类型**: 支持多种字段类型，每种类型都有对应的配置选项
5. **排序**: 表单字段按sort_order排序显示
