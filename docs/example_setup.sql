-- 动态表单系统示例数据
-- 这个脚本演示如何为不同的实体类型设置属性定义

-- 1. 产品实体 (product) 的属性定义
INSERT INTO `sys_attribute_definitions` (`entity_type`, `attr_code`, `attr_name`, `field_type`, `data_type`, `is_required`, `config`, `sort_order`) VALUES
('product', 'title', '产品标题', 'text', 'string', 1, '{"placeholder": "请输入产品标题", "max_length": 100}', 1),
('product', 'price', '价格', 'number', 'decimal', 1, '{"min": 0, "step": 0.01, "placeholder": "请输入价格"}', 2),
('product', 'description', '产品描述', 'textarea', 'string', 0, '{"placeholder": "请输入产品描述", "rows": 4}', 3),
('product', 'category', '分类', 'select', 'string', 1, '{"options": [{"label": "手机", "value": "phone"}, {"label": "电脑", "value": "computer"}, {"label": "平板", "value": "tablet"}]}', 4),
('product', 'tags', '标签', 'multi_select', 'array', 0, '{"options": [{"label": "热销", "value": "hot"}, {"label": "新品", "value": "new"}, {"label": "推荐", "value": "recommend"}]}', 5),
('product', 'is_featured', '是否推荐', 'checkbox', 'boolean', 0, '{"label": "设为推荐产品"}', 6),
('product', 'main_image', '主图', 'single_image', 'string', 0, '{"accept": "image/*", "max_size": 5242880}', 7),
('product', 'gallery', '产品图册', 'multi_image', 'array', 0, '{"accept": "image/*", "max_size": 5242880, "max_count": 10}', 8),
('product', 'launch_date', '上市日期', 'date', 'string', 0, '{"placeholder": "选择上市日期"}', 9),
('product', 'specifications', '规格参数', 'rich_text', 'string', 0, '{"toolbar": ["bold", "italic", "underline", "link", "bulletedList", "numberedList"]}', 10);

-- 2. 文章实体 (article) 的属性定义
INSERT INTO `sys_attribute_definitions` (`entity_type`, `attr_code`, `attr_name`, `field_type`, `data_type`, `is_required`, `config`, `sort_order`) VALUES
('article', 'title', '文章标题', 'text', 'string', 1, '{"placeholder": "请输入文章标题", "max_length": 200}', 1),
('article', 'summary', '文章摘要', 'textarea', 'string', 1, '{"placeholder": "请输入文章摘要", "rows": 3, "max_length": 500}', 2),
('article', 'content', '文章内容', 'rich_text', 'string', 1, '{"toolbar": ["heading", "bold", "italic", "underline", "link", "bulletedList", "numberedList", "blockQuote", "insertTable", "mediaEmbed"]}', 3),
('article', 'category', '文章分类', 'select', 'string', 1, '{"options": [{"label": "技术", "value": "tech"}, {"label": "产品", "value": "product"}, {"label": "公司动态", "value": "news"}]}', 4),
('article', 'tags', '标签', 'multi_select', 'array', 0, '{"options": [{"label": "前端", "value": "frontend"}, {"label": "后端", "value": "backend"}, {"label": "移动端", "value": "mobile"}, {"label": "AI", "value": "ai"}]}', 5),
('article', 'cover_image', '封面图', 'single_image', 'string', 0, '{"accept": "image/*", "max_size": 2097152}', 6),
('article', 'is_published', '是否发布', 'radio', 'boolean', 1, '{"options": [{"label": "发布", "value": true}, {"label": "草稿", "value": false}]}', 7),
('article', 'publish_time', '发布时间', 'datetime', 'string', 0, '{"placeholder": "选择发布时间"}', 8),
('article', 'author', '作者', 'text', 'string', 1, '{"placeholder": "请输入作者姓名"}', 9),
('article', 'reading_time', '预计阅读时间(分钟)', 'number', 'integer', 0, '{"min": 1, "max": 120, "placeholder": "预计阅读时间"}', 10);

-- 3. 用户资料实体 (user_profile) 的属性定义
INSERT INTO `sys_attribute_definitions` (`entity_type`, `attr_code`, `attr_name`, `field_type`, `data_type`, `is_required`, `config`, `sort_order`) VALUES
('user_profile', 'real_name', '真实姓名', 'text', 'string', 1, '{"placeholder": "请输入真实姓名", "max_length": 50}', 1),
('user_profile', 'gender', '性别', 'radio', 'string', 1, '{"options": [{"label": "男", "value": "male"}, {"label": "女", "value": "female"}]}', 2),
('user_profile', 'birth_date', '出生日期', 'date', 'string', 0, '{"placeholder": "选择出生日期"}', 3),
('user_profile', 'phone', '手机号码', 'text', 'string', 1, '{"placeholder": "请输入手机号码", "pattern": "^1[3-9]\\\\d{9}$"}', 4),
('user_profile', 'email', '邮箱地址', 'text', 'string', 1, '{"placeholder": "请输入邮箱地址", "type": "email"}', 5),
('user_profile', 'address', '联系地址', 'textarea', 'string', 0, '{"placeholder": "请输入详细地址", "rows": 2}', 6),
('user_profile', 'avatar', '头像', 'single_image', 'string', 0, '{"accept": "image/*", "max_size": 1048576}', 7),
('user_profile', 'bio', '个人简介', 'textarea', 'string', 0, '{"placeholder": "请输入个人简介", "rows": 4, "max_length": 500}', 8),
('user_profile', 'interests', '兴趣爱好', 'multi_select', 'array', 0, '{"options": [{"label": "编程", "value": "programming"}, {"label": "阅读", "value": "reading"}, {"label": "运动", "value": "sports"}, {"label": "音乐", "value": "music"}, {"label": "旅行", "value": "travel"}]}', 9),
('user_profile', 'is_public', '公开资料', 'checkbox', 'boolean', 0, '{"label": "允许其他用户查看我的资料"}', 10);

-- 4. 活动实体 (event) 的属性定义
INSERT INTO `sys_attribute_definitions` (`entity_type`, `attr_code`, `attr_name`, `field_type`, `data_type`, `is_required`, `config`, `sort_order`) VALUES
('event', 'title', '活动标题', 'text', 'string', 1, '{"placeholder": "请输入活动标题", "max_length": 100}', 1),
('event', 'description', '活动描述', 'rich_text', 'string', 1, '{"toolbar": ["bold", "italic", "underline", "link", "bulletedList", "numberedList"]}', 2),
('event', 'event_type', '活动类型', 'select', 'string', 1, '{"options": [{"label": "会议", "value": "conference"}, {"label": "培训", "value": "training"}, {"label": "聚会", "value": "party"}, {"label": "展览", "value": "exhibition"}]}', 3),
('event', 'start_time', '开始时间', 'datetime', 'string', 1, '{"placeholder": "选择开始时间"}', 4),
('event', 'end_time', '结束时间', 'datetime', 'string', 1, '{"placeholder": "选择结束时间"}', 5),
('event', 'location', '活动地点', 'text', 'string', 1, '{"placeholder": "请输入活动地点"}', 6),
('event', 'max_participants', '最大参与人数', 'number', 'integer', 0, '{"min": 1, "placeholder": "最大参与人数"}', 7),
('event', 'registration_fee', '报名费用', 'number', 'decimal', 0, '{"min": 0, "step": 0.01, "placeholder": "报名费用"}', 8),
('event', 'poster', '活动海报', 'single_image', 'string', 0, '{"accept": "image/*", "max_size": 3145728}', 9),
('event', 'is_online', '线上活动', 'checkbox', 'boolean', 0, '{"label": "这是一个线上活动"}', 10);

-- 查询示例：获取产品实体的所有属性定义
-- SELECT * FROM sys_attribute_definitions WHERE entity_type = 'product' ORDER BY sort_order;

-- 查询示例：获取所有实体类型
-- SELECT DISTINCT entity_type FROM sys_attribute_definitions;

-- 示例动态实体数据
-- 注意：实际使用时应该通过API创建，这里仅作为示例
INSERT INTO `sys_dynamic_entities` (`entity_type`, `attributes`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('product', '{"title": "iPhone 15 Pro", "price": 7999.00, "description": "最新款iPhone Pro，配备A17 Pro芯片", "category": "phone", "tags": ["hot", "new"], "is_featured": true, "launch_date": "2023-09-22"}', '0', 'admin', NOW(), 'admin', NOW()),
('article', '{"title": "Go语言动态表单系统设计", "summary": "介绍如何使用Go语言设计一个灵活的动态表单系统", "content": "<h2>概述</h2><p>动态表单系统是现代Web应用中的重要组件...</p>", "category": "tech", "tags": ["backend", "go"], "is_published": true, "author": "张三", "reading_time": 15}', '0', 'admin', NOW(), 'admin', NOW()),
('user_profile', '{"real_name": "李四", "gender": "male", "phone": "13800138000", "email": "<EMAIL>", "bio": "资深软件工程师，专注于后端开发", "interests": ["programming", "reading"], "is_public": true}', '0', 'lisi', NOW(), 'lisi', NOW()),
('event', '{"title": "Go语言技术分享会", "description": "<p>分享Go语言在微服务架构中的应用实践</p>", "event_type": "conference", "start_time": "2024-02-15T14:00:00", "end_time": "2024-02-15T17:00:00", "location": "北京市朝阳区科技园", "max_participants": 100, "registration_fee": 0, "is_online": false}', '0', 'admin', NOW(), 'admin', NOW());
