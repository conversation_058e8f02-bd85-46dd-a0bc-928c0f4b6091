package dao

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/tools/convert"
	"strings"
	"time"

	"gorm.io/gorm"
)

// CreateRole 创建角色
func (d *Dao) CreateRole(param *req.CreateRoleRequest) error {
	now := time.Now()
	role := model.SysRole{
		RoleName:          param.RoleName,
		RoleKey:           param.RoleKey,
		RoleSort:          convert.StrTo(param.RoleSort).MustInt(),
		MenuCheckStrictly: convert.StrTo(param.MenuCheckStrictly).MustInt(),
		DeptCheckStrictly: convert.StrTo(param.DeptCheckStrictly).MustInt(),
		Status:            param.Status,
		DelFlag:           "0",
		CreateBy:          param.Operater,
		CreateTime:        now,
		UpdateBy:          param.Operater,
		UpdateTime:        now,
		Remark:            param.Remark,
	}
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}
	if err := tx.Create(&role).Error; err != nil {
		tx.Rollback()
		return err
	}
	if param.MenuIds != "" {
		menuIds := strings.Split(param.MenuIds, ",")
		for i := range menuIds {
			roleMenu := model.SysRoleMenu{
				RoleId: role.RoleId,
				MenuId: convert.StrTo(menuIds[i]).MustUInt64(),
			}
			if err := tx.Create(&roleMenu).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if param.DeptIds != "" {
		deptIds := strings.Split(param.DeptIds, ",")
		for i := range deptIds {
			roleDept := model.SysRoleDept{
				RoleId: role.RoleId,
				DeptId: convert.StrTo(deptIds[i]).MustUInt64(),
			}
			if err := tx.Create(&roleDept).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	return tx.Commit().Error
}

// UpdateRole 更新角色
func (d *Dao) UpdateRole(id uint64, values map[string]interface{}, menuIds, deptIds string) error {
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}
	err := tx.Model(&model.SysRole{}).
		Where("role_id = ?", id).
		Updates(values).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	if menuIds != "" {
		tx.Exec("delete from role_menu where role_id=?", id)
		menuIdList := strings.Split(menuIds, ",")
		for i := range menuIdList {
			roleMenu := model.SysRoleMenu{
				RoleId: id,
				MenuId: convert.StrTo(menuIdList[i]).MustUInt64(),
			}
			if err := tx.Create(&roleMenu).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if deptIds != "" {
		tx.Exec("delete from role_dept where role_id=?", id)
		deptIdList := strings.Split(deptIds, ",")
		for i := range deptIdList {
			roleDept := model.SysRoleDept{
				RoleId: id,
				DeptId: convert.StrTo(deptIdList[i]).MustUInt64(),
			}
			if err := tx.Create(&roleDept).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	return tx.Commit().Error
}

// 批量删除
func (d *Dao) DeleteRoleByIds(ids []uint64, operater string) error {
	if len(ids) == 0 {
		return nil
	}
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}

	values := map[string]interface{}{
		"del_flag":    "2",
		"update_by":   operater,
		"update_time": time.Now(),
	}
	err := tx.Model(&model.SysRole{}).
		Where("role_id IN (?)", ids).
		Updates(values).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("delete from role_menu where role_id IN (?)", ids).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("delete from role_dept where role_id IN (?)", ids).Error; err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// GetRoleById 根据ID获取角色
func (d *Dao) GetRoleById(roleId uint64, fields []string) (*model.SysRole, error) {
	var role model.SysRole
	query := d.engine.Where("role_id = ? AND del_flag = ?", roleId, "0")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&role).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("角色不存在")
	}
	return &role, err
}

// GetRoleByRoleIds 根据角色ID列表获取角色
func (d *Dao) GetRoleByRoleIds(ids []uint64, fields []string) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	query := d.engine.Where("role_id IN (?) AND del_flag = ?", ids, "0")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Find(&roles).Error
	return roles, err
}

// GetRoleByRoleName 根据角色名获取角色
func (d *Dao) GetRoleByRoleName(roleName string, fields []string) (*model.SysRole, error) {
	var role model.SysRole
	query := d.engine.Where("role_name = ? AND del_flag = ?", roleName, "0")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&role).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("角色不存在")
	}
	return &role, err
}

// GetRoleByRoleKey 根据角色key获取角色
func (d *Dao) GetRoleByRoleKey(roleKey string, fields []string) (*model.SysRole, error) {
	var role model.SysRole
	query := d.engine.Where("role_key = ? AND del_flag = ?", roleKey, "0")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&role).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("角色不存在")
	}
	return &role, err
}

// GetRoleAll 获取角色列表
func (d *Dao) GetRoleAll(fields []string) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	query := d.engine.Where("del_flag = ?", "0")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Find(&roles).Error
	return roles, err
}

// GetRoleList 获取角色列表
func (d *Dao) GetRoleList(param *req.RoleListRequest, fields []string, pageOption *app.PageOption) (int64, []*model.SysRole, error) {
	var roles []*model.SysRole
	query := d.engine.Model(&model.SysRole{}).Where("del_flag = ?", "0")
	if param.RoleId != 0 {
		query = query.Where("role_id = ?", param.RoleId)
	}
	if param.RoleName != "" {
		query = query.Where("role_name like ?", "%"+param.RoleName+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.RoleKey != "" {
		query = query.Where("role_key = ?", param.RoleKey)
	}
	if param.BeginTime != "" {
		bTime, err := convert.StrTo(param.BeginTime).MustDate()
		if err != nil {
			return 0, nil, err
		}
		query = query.Where("create_time >= ?", bTime)
	}
	if param.EndTime != "" {
		eTime, err := convert.StrTo(param.EndTime).MustDate()
		if err != nil {
			return 0, nil, err
		}
		query = query.Where("create_time <= ?", eTime)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	// 设置默认排序
	if pageOption.OrderBy == "" {
		pageOption.OrderBy = "role_sort"
	}
	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return 0, nil, err
	}
	err = query.Find(&roles).Error
	return total, roles, err
}

// GetRolesByUserId 根据用户ID获取角色列表
func (d *Dao) GetRolesByUserId(userId uint64, fields []string) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	query := d.engine.Table("sys_role r").
		Joins("left join sys_user_role ur on r.role_id = ur.role_id").
		Where("ur.user_id = ? AND r.del_flag = ?", userId, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("r.role_sort").Find(&roles).Error
	return roles, err
}

// GetRoleDepts 获取角色部门列表
func (d *Dao) GetRoleDepts(roleId uint64) ([]*model.SysRoleDept, error) {
	var roleDepts []*model.SysRoleDept
	err := d.engine.Where("role_id = ?", roleId).Find(&roleDepts).Error
	return roleDepts, err
}

// BatchInsertRoleDept 批量插入角色部门关联
func (d *Dao) BatchInsertRoleDept(roleDepts []*model.SysRoleDept) error {
	return d.engine.Create(&roleDepts).Error
}

// DeleteRoleDeptByRoleId 删除角色部门关联
func (d *Dao) DeleteRoleDeptByRoleId(roleId uint64) error {
	return d.engine.Where("role_id = ?", roleId).Delete(&model.SysRoleDept{}).Error
}

// GetRoleDeptsByRoleIds 根据角色ID列表获取角色部门列表
func (d *Dao) GetRoleDeptsByRoleIds(roleIds []uint64) (map[uint64][]*model.SysDept, error) {
	var results []struct {
		RoleId uint64 `gorm:"column:role_id"`
		model.SysDept
	}

	err := d.engine.Table("sys_role_dept rd").
		Select("rd.role_id, d.*").
		Joins("LEFT JOIN sys_dept d ON rd.dept_id = d.dept_id").
		Where("rd.role_id IN (?) AND d.status = ? AND d.del_flag = ?", roleIds, "0", "0").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	roleDeptMap := make(map[uint64][]*model.SysDept)
	for _, result := range results {
		dept := &model.SysDept{
			DeptId:     result.DeptId,
			ParentId:   result.ParentId,
			Ancestors:  result.Ancestors,
			DeptName:   result.DeptName,
			OrderNum:   result.OrderNum,
			Leader:     result.Leader,
			Phone:      result.Phone,
			Email:      result.Email,
			Status:     result.Status,
			DelFlag:    result.DelFlag,
			CreateBy:   result.CreateBy,
			CreateTime: result.CreateTime,
			UpdateBy:   result.UpdateBy,
			UpdateTime: result.UpdateTime,
		}
		roleDeptMap[result.RoleId] = append(roleDeptMap[result.RoleId], dept)
	}
	return roleDeptMap, nil
}

func (d *Dao) GetRoleMenusByRoleIds(roleIds []uint64) (map[uint64][]*model.SysMenu, error) {
	var results []struct {
		RoleId uint64 `gorm:"column:role_id"`
		model.SysMenu
	}

	err := d.engine.Table("sys_role_menu rm").
		Select("rm.role_id, m.*").
		Joins("LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id").
		Where("rm.role_id IN (?) AND m.status = ?", roleIds, "0").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	roleMenuMap := make(map[uint64][]*model.SysMenu)
	for _, result := range results {
		menu := &model.SysMenu{
			MenuId:     result.MenuId,
			MenuName:   result.MenuName,
			ParentId:   result.ParentId,
			OrderNum:   result.OrderNum,
			Path:       result.Path,
			Component:  result.Component,
			Query:      result.Query,
			RouteName:  result.RouteName,
			IsFrame:    result.IsFrame,
			IsCache:    result.IsCache,
			MenuType:   result.MenuType,
			Visible:    result.Visible,
			Status:     result.Status,
			Perms:      result.Perms,
			Icon:       result.Icon,
			CreateBy:   result.CreateBy,
			CreateTime: result.CreateTime,
			UpdateBy:   result.UpdateBy,
			UpdateTime: result.UpdateTime,
			Remark:     result.Remark,
		}
		roleMenuMap[result.RoleId] = append(roleMenuMap[result.RoleId], menu)
	}
	return roleMenuMap, nil
}

// GetRoleIdsByMenuId 根据菜单id获取角色id
func (d *Dao) GetRoleIdsByMenuId(menuId uint64) ([]uint64, error) {
	roleIds := make([]uint64, 0, 8)
	err := d.engine.Model(&model.SysRoleMenu{}).Select("distinct role_id").
		Where("menu_id = ?", menuId).
		Pluck("role_id", &roleIds).Error
	if err != nil {
		return nil, err
	}
	return roleIds, nil
}

// GetUserIdsByRoleId 根据角色id获取用户id
func (d *Dao) GetUserIdsByRoleId(roleId uint64) ([]uint64, error) {
	userIds := make([]uint64, 0, 8)
	err := d.engine.Model(&model.SysUserRole{}).Select("distinct user_id").
		Where("role_id = ?", roleId).
		Pluck("user_id", &userIds).Error
	if err != nil {
		return nil, err
	}
	return userIds, nil
}

// 批量写入
func (d *Dao) BatchUserRole(rows []string) error {
	if len(rows) == 0 {
		return nil
	}
	//rows 格式：userId,roleId
	buf := make([]*model.SysUserRole, 0, len(rows))
	for _, row := range rows {
		r := strings.Split(row, ",")
		if len(r) != 2 {
			continue
		}
		buf = append(buf, &model.SysUserRole{
			UserId: convert.StrTo(r[0]).MustUInt64(),
			RoleId: convert.StrTo(r[1]).MustUInt64(),
		})
	}
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}
	if err := tx.Table("user_role").Create(&buf).Error; err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}
