package dao

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"

	"gorm.io/gorm"
)

// GetAttributeDefinitionById 根据ID获取属性定义
func (d *Dao) GetAttributeDefinitionById(id uint64, fields []string) (*model.SysAttributeDefinitions, error) {
	var attr model.SysAttributeDefinitions
	query := d.engine.Where("id = ?", id)

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&attr).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("属性定义不存在")
	}
	return &attr, err
}

// GetAttributeDefinitionByCode 根据实体类型和属性代码获取属性定义
func (d *Dao) GetAttributeDefinitionByCode(entityType, attrCode string, fields []string) (*model.SysAttributeDefinitions, error) {
	var attr model.SysAttributeDefinitions
	query := d.engine.Where("entity_type = ? AND attr_code = ?", entityType, attrCode)

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&attr).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("属性定义不存在")
	}
	return &attr, err
}

// CreateAttributeDefinition 创建属性定义
func (d *Dao) CreateAttributeDefinition(param *req.CreateAttributeDefinitionRequest) (*model.SysAttributeDefinitions, error) {
	// 检查属性代码是否已存在
	existing, _ := d.GetAttributeDefinitionByCode(param.EntityType, param.AttrCode, []string{"id"})
	if existing != nil {
		return nil, errors.New("属性代码已存在")
	}

	attr := model.SysAttributeDefinitions{
		EntityType: param.EntityType,
		AttrCode:   param.AttrCode,
		AttrName:   param.AttrName,
		FieldType:  param.FieldType,
		DataType:   param.DataType,
		IsRequired: param.IsRequired,
		Config:     param.Config,
		SortOrder:  param.SortOrder,
	}

	err := d.engine.Create(&attr).Error
	if err != nil {
		return nil, err
	}

	return &attr, nil
}

// UpdateAttributeDefinition 更新属性定义
func (d *Dao) UpdateAttributeDefinition(id uint64, param *req.UpdateAttributeDefinitionRequest) error {
	// 检查记录是否存在
	existing, err := d.GetAttributeDefinitionById(id, []string{"id", "entity_type", "attr_code"})
	if err != nil {
		return err
	}

	// 如果更新了属性代码，检查新代码是否已存在
	if param.AttrCode != "" && param.AttrCode != existing.AttrCode {
		entityType := param.EntityType
		if entityType == "" {
			entityType = existing.EntityType
		}

		duplicate, _ := d.GetAttributeDefinitionByCode(entityType, param.AttrCode, []string{"id"})
		if duplicate != nil && duplicate.Id != id {
			return errors.New("属性代码已存在")
		}
	}

	// 构建更新数据
	updates := make(map[string]interface{})

	if param.EntityType != "" {
		updates["entity_type"] = param.EntityType
	}
	if param.AttrCode != "" {
		updates["attr_code"] = param.AttrCode
	}
	if param.AttrName != "" {
		updates["attr_name"] = param.AttrName
	}
	if param.FieldType != "" {
		updates["field_type"] = param.FieldType
	}
	if param.DataType != "" {
		updates["data_type"] = param.DataType
	}
	if param.IsRequired != nil {
		updates["is_required"] = *param.IsRequired
	}
	if param.Config != "" {
		updates["config"] = param.Config
	}
	if param.SortOrder != nil {
		updates["sort_order"] = *param.SortOrder
	}

	if len(updates) == 0 {
		return errors.New("没有需要更新的字段")
	}

	return d.engine.Model(&model.SysAttributeDefinitions{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// DeleteAttributeDefinitionById 根据ID删除属性定义
func (d *Dao) DeleteAttributeDefinitionById(id uint64) error {
	if id == 0 {
		return errors.New("无效的ID")
	}

	// 检查记录是否存在
	_, err := d.GetAttributeDefinitionById(id, []string{"id"})
	if err != nil {
		return err
	}

	return d.engine.Delete(&model.SysAttributeDefinitions{}, id).Error
}

// DeleteAttributeDefinitionByIds 批量删除属性定义
func (d *Dao) DeleteAttributeDefinitionByIds(ids []uint64) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	return d.engine.Delete(&model.SysAttributeDefinitions{}, ids).Error
}

// GetAttributeDefinitionList 获取属性定义列表
func (d *Dao) GetAttributeDefinitionList(param *req.AttributeDefinitionListRequest, fields []string, pageOption *app.PageOption) ([]*model.SysAttributeDefinitions, int64, error) {
	var attrs []*model.SysAttributeDefinitions
	query := d.engine.Model(&model.SysAttributeDefinitions{})

	// 添加查询条件
	if param.EntityType != "" {
		query = query.Where("entity_type = ?", param.EntityType)
	}
	if param.AttrCode != "" {
		query = query.Where("attr_code LIKE ?", "%"+param.AttrCode+"%")
	}
	if param.AttrName != "" {
		query = query.Where("attr_name LIKE ?", "%"+param.AttrName+"%")
	}
	if param.FieldType != "" {
		query = query.Where("field_type = ?", param.FieldType)
	}
	if param.DataType != "" {
		query = query.Where("data_type = ?", param.DataType)
	}
	if param.IsRequired != nil {
		query = query.Where("is_required = ?", *param.IsRequired)
	}

	// 默认按排序字段和ID排序
	if pageOption.OrderBy == "" {
		pageOption.OrderBy = "sort_order ASC, id ASC"
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return nil, 0, err
	}

	err = query.Find(&attrs).Error
	return attrs, total, err
}

// GetAttributeDefinitionsByEntity 根据实体类型获取属性定义
func (d *Dao) GetAttributeDefinitionsByEntity(entityType string, isRequired *int, fields []string) ([]*model.SysAttributeDefinitions, error) {
	var attrs []*model.SysAttributeDefinitions
	query := d.engine.Where("entity_type = ?", entityType)

	if isRequired != nil {
		query = query.Where("is_required = ?", *isRequired)
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("sort_order ASC, id ASC").Find(&attrs).Error
	return attrs, err
}

// GetEntityTypes 获取所有实体类型
func (d *Dao) GetEntityTypes() ([]string, error) {
	var entityTypes []string
	err := d.engine.Model(&model.SysAttributeDefinitions{}).
		Distinct("entity_type").
		Pluck("entity_type", &entityTypes).Error
	return entityTypes, err
}

// CheckAttributeCodeExists 检查属性代码是否存在
func (d *Dao) CheckAttributeCodeExists(entityType, attrCode string, excludeId uint64) (bool, error) {
	var count int64
	query := d.engine.Model(&model.SysAttributeDefinitions{}).
		Where("entity_type = ? AND attr_code = ?", entityType, attrCode)

	if excludeId > 0 {
		query = query.Where("id != ?", excludeId)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// CopyAttributeDefinitions 复制属性定义到新的实体类型
func (d *Dao) CopyAttributeDefinitions(sourceEntityType, targetEntityType string) error {
	// 获取源实体的所有属性定义
	sourceAttrs, err := d.GetAttributeDefinitionsByEntity(sourceEntityType, nil, nil)
	if err != nil {
		return err
	}

	if len(sourceAttrs) == 0 {
		return errors.New("源实体类型没有属性定义")
	}

	// 检查目标实体类型是否已有属性定义
	existingAttrs, _ := d.GetAttributeDefinitionsByEntity(targetEntityType, nil, []string{"id"})
	if len(existingAttrs) > 0 {
		return errors.New("目标实体类型已有属性定义")
	}

	// 批量创建新的属性定义
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	for _, sourceAttr := range sourceAttrs {
		newAttr := model.SysAttributeDefinitions{
			EntityType: targetEntityType,
			AttrCode:   sourceAttr.AttrCode,
			AttrName:   sourceAttr.AttrName,
			FieldType:  sourceAttr.FieldType,
			DataType:   sourceAttr.DataType,
			IsRequired: sourceAttr.IsRequired,
			Config:     sourceAttr.Config,
			SortOrder:  sourceAttr.SortOrder,
		}

		if err := tx.Create(&newAttr).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// BatchUpdateSortOrder 批量更新排序
func (d *Dao) BatchUpdateSortOrder(items []req.SortOrderItem) error {
	if len(items) == 0 {
		return errors.New("更新项目不能为空")
	}

	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	for _, item := range items {
		err := tx.Model(&model.SysAttributeDefinitions{}).
			Where("id = ?", item.Id).
			Update("sort_order", item.SortOrder).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetAttributeDefinitionStats 获取属性定义统计信息
func (d *Dao) GetAttributeDefinitionStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总数
	var totalCount int64
	if err := d.engine.Model(&model.SysAttributeDefinitions{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total_count"] = totalCount

	// 按实体类型统计
	var entityTypeStats []struct {
		EntityType string `json:"entity_type"`
		Count      int64  `json:"count"`
	}
	if err := d.engine.Model(&model.SysAttributeDefinitions{}).
		Select("entity_type, COUNT(*) as count").
		Group("entity_type").
		Find(&entityTypeStats).Error; err != nil {
		return nil, err
	}

	entityTypeMap := make(map[string]int64)
	for _, stat := range entityTypeStats {
		entityTypeMap[stat.EntityType] = stat.Count
	}
	stats["entity_type_stats"] = entityTypeMap

	// 按字段类型统计
	var fieldTypeStats []struct {
		FieldType string `json:"field_type"`
		Count     int64  `json:"count"`
	}
	if err := d.engine.Model(&model.SysAttributeDefinitions{}).
		Select("field_type, COUNT(*) as count").
		Group("field_type").
		Find(&fieldTypeStats).Error; err != nil {
		return nil, err
	}

	fieldTypeMap := make(map[string]int64)
	for _, stat := range fieldTypeStats {
		fieldTypeMap[stat.FieldType] = stat.Count
	}
	stats["field_type_stats"] = fieldTypeMap

	// 按数据类型统计
	var dataTypeStats []struct {
		DataType string `json:"data_type"`
		Count    int64  `json:"count"`
	}
	if err := d.engine.Model(&model.SysAttributeDefinitions{}).
		Select("data_type, COUNT(*) as count").
		Group("data_type").
		Find(&dataTypeStats).Error; err != nil {
		return nil, err
	}

	dataTypeMap := make(map[string]int64)
	for _, stat := range dataTypeStats {
		dataTypeMap[stat.DataType] = stat.Count
	}
	stats["data_type_stats"] = dataTypeMap

	// 按是否必填统计
	var requiredStats []struct {
		IsRequired int   `json:"is_required"`
		Count      int64 `json:"count"`
	}
	if err := d.engine.Model(&model.SysAttributeDefinitions{}).
		Select("is_required, COUNT(*) as count").
		Group("is_required").
		Find(&requiredStats).Error; err != nil {
		return nil, err
	}

	requiredMap := make(map[string]int64)
	for _, stat := range requiredStats {
		if stat.IsRequired == 1 {
			requiredMap["required"] = stat.Count
		} else {
			requiredMap["optional"] = stat.Count
		}
	}
	stats["required_stats"] = requiredMap

	return stats, nil
}
