package dao

import (
	"encoding/json"
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"time"

	"gorm.io/gorm"
)

// GetDynamicEntityById 根据ID获取动态实体
func (d *Dao) GetDynamicEntityById(id uint64, fields []string) (*model.SysDynamicEntities, error) {
	var entity model.SysDynamicEntities
	query := d.engine.Where("id = ?", id)

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&entity).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("动态实体不存在")
	}
	return &entity, err
}

// CreateDynamicEntity 创建动态实体
func (d *Dao) CreateDynamicEntity(param *req.CreateDynamicEntityRequest) (*model.SysDynamicEntities, error) {
	// 将属性转换为JSON字符串
	attributesJSON, err := json.Marshal(param.Attributes)
	if err != nil {
		return nil, errors.New("属性数据格式错误")
	}

	now := time.Now()
	entity := model.SysDynamicEntities{
		EntityType: param.EntityType,
		Attributes: string(attributesJSON),
		Status:     param.Status,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
	}

	err = d.engine.Create(&entity).Error
	if err != nil {
		return nil, err
	}

	return &entity, nil
}

// UpdateDynamicEntity 更新动态实体
func (d *Dao) UpdateDynamicEntity(id uint64, param *req.UpdateDynamicEntityRequest) error {
	// 检查记录是否存在
	existing, err := d.GetDynamicEntityById(id, []string{"id"})
	if err != nil {
		return err
	}
	if existing == nil {
		return errors.New("动态实体不存在")
	}

	// 构建更新数据
	updates := make(map[string]interface{})

	if param.EntityType != "" {
		updates["entity_type"] = param.EntityType
	}

	if param.Attributes != nil {
		attributesJSON, err := json.Marshal(param.Attributes)
		if err != nil {
			return errors.New("属性数据格式错误")
		}
		updates["attributes"] = string(attributesJSON)
	}

	if param.Status != "" {
		updates["status"] = param.Status
	}

	updates["update_by"] = param.Operater
	updates["update_time"] = time.Now()

	return d.engine.Model(&model.SysDynamicEntities{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteDynamicEntity 删除动态实体
func (d *Dao) DeleteDynamicEntity(id uint64) error {
	return d.engine.Where("id = ?", id).Delete(&model.SysDynamicEntities{}).Error
}

// DeleteDynamicEntities 批量删除动态实体
func (d *Dao) DeleteDynamicEntities(ids []uint64) error {
	return d.engine.Where("id IN ?", ids).Delete(&model.SysDynamicEntities{}).Error
}

// GetDynamicEntityList 获取动态实体列表
func (d *Dao) GetDynamicEntityList(param *req.DynamicEntityListRequest, fields []string, pageOption *app.PageOption) ([]*model.SysDynamicEntities, int64, error) {
	var entities []*model.SysDynamicEntities
	query := d.engine.Model(&model.SysDynamicEntities{})

	// 添加查询条件
	if param.EntityType != "" {
		query = query.Where("entity_type = ?", param.EntityType)
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.CreateBy != "" {
		query = query.Where("create_by LIKE ?", "%"+param.CreateBy+"%")
	}
	if param.BeginTime != "" {
		query = query.Where("create_time >= ?", param.BeginTime)
	}
	if param.EndTime != "" {
		query = query.Where("create_time <= ?", param.EndTime)
	}

	// 获取总数
	var total int64
	if pageOption.NeedTotal {
		err := query.Count(&total).Error
		if err != nil {
			return nil, 0, err
		}
	}

	// 字段选择
	if len(fields) > 0 {
		query = query.Select(fields)
	}

	// 排序
	if pageOption.OrderBy != "" {
		query = query.Order(pageOption.OrderBy)
	} else {
		query = query.Order("id DESC")
	}

	// 分页
	if pageOption.Page > 0 && pageOption.PageSize > 0 {
		offset := (pageOption.Page - 1) * pageOption.PageSize
		query = query.Offset(offset).Limit(pageOption.PageSize)
	}

	err := query.Find(&entities).Error
	return entities, total, err
}

// GetDynamicEntitiesByType 根据实体类型获取动态实体列表
func (d *Dao) GetDynamicEntitiesByType(entityType string, fields []string) ([]*model.SysDynamicEntities, error) {
	var entities []*model.SysDynamicEntities
	query := d.engine.Where("entity_type = ? AND status = ?", entityType, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("id DESC").Find(&entities).Error
	return entities, err
}
