package dao

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/tools"
	"time"

	"gorm.io/gorm"
)

// GetUserByUserId 根据用户ID获取用户信息
func (d *Dao) GetUserByUserId(userId uint64, status string, fields []string) (*model.SysUser, error) {
	var user model.SysUser
	query := d.engine.Where("user_id = ? AND del_flag = ?", userId, 0)
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&user).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("用户不存在")
	}
	return &user, err
}

// GetUserByUserName 根据用户名获取用户信息
func (d *Dao) GetUserByUserName(username, status string, fields []string) (*model.SysUser, error) {
	var user model.SysUser

	query := d.engine.Where("user_name = ? AND del_flag = ?", username, 0)
	if status != "" {
		query = query.Where("status = ?", status)
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&user).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("用户不存在")
	}
	return &user, err
}

// GetUserByPhoneNumber 根据手机号获取用户信息
func (d *Dao) GetUserByPhoneNumber(phoneNumber, status string, fields []string) (*model.SysUser, error) {
	var user model.SysUser

	query := d.engine.Where("phonenumber = ? AND del_flag = ?", phoneNumber, 0)
	if status != "" {
		query = query.Where("status = ?", status)
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&user).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("用户不存在")
	}
	return &user, err
}

// GetAdminByTenant 获取管理员信息
func (d *Dao) GetAdminByTenant(fields []string) (*model.SysUser, error) {
	var user model.SysUser
	query := d.engine.Where("is_admin = ?", 1)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&user).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("用户不存在")
	}
	return &user, err
}

// GetUserByEmail 根据邮箱获取用户信息
func (d *Dao) GetUserByEmail(email, status string, fields []string) (*model.SysUser, error) {
	var user model.SysUser

	query := d.engine.Where("email = ? AND del_flag = ?", email, 0)
	if status != "" {
		query = query.Where("status = ?", status)
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&user).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("用户不存在")
	}
	return &user, err
}

// CreateUser 创建用户
func (d *Dao) CreateUser(param *req.CreateUserRequest, postIds, roleIds []uint64) error {
	now := time.Now()
	salt := tools.GenerateSubId(6)
	pwdNew := param.UserName + param.Password + salt
	pwdNew = tools.MustEncryptString(pwdNew)
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	user := model.SysUser{
		DeptId:      param.DeptId,
		UserName:    param.UserName,
		NickName:    param.NickName,
		UserType:    "00",
		Email:       param.Email,
		Phonenumber: param.PhoneNumber,
		Sex:         param.Sex,
		Avatar:      param.Avatar,
		Password:    pwdNew,
		Salt:        salt,
		Status:      param.Status,
		DelFlag:     "0",
		LoginIp:     "",
		LoginDate:   now,
		CreateBy:    param.Operater,
		CreateTime:  now,
		Remark:      param.Remark,
		UpdateBy:    param.Operater,
		UpdateTime:  now,
	}
	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		return err
	}

	if len(postIds) > 0 {
		for i := range postIds {
			userPost := model.SysUserPost{
				UserId: user.UserId,
				PostId: postIds[i],
			}
			if err := tx.Create(&userPost).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	if len(roleIds) > 0 {
		for i := range roleIds {
			userRole := model.SysUserRole{
				UserId: user.UserId,
				RoleId: roleIds[i],
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	return tx.Commit().Error
}

func (d *Dao) UpdateUser(id uint64, values map[string]interface{}, postIds, roleIds []uint64) error {
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}
	err := d.engine.Model(&model.SysUser{}).
		Where("user_id = ?", id).
		Updates(values).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	if len(postIds) > 0 {
		tx.Exec("delete from user_post where user_id=?", id)
		for i := range postIds {
			userPost := model.SysUserPost{
				UserId: id,
				PostId: postIds[i],
			}
			if err := tx.Create(&userPost).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	if len(roleIds) > 0 {
		tx.Exec("delete from user_role where user_id=?", id)
		for i := range roleIds {
			userRole := model.SysUserRole{
				UserId: id,
				RoleId: roleIds[i],
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	return tx.Commit().Error
}

// 单个删除
func (d *Dao) DeleteUserById(id uint64, operater string) error {
	if id == 0 {
		return nil
	}
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}
	values := map[string]interface{}{
		"del_flag":    "2",
		"update_by":   operater,
		"update_time": time.Now(),
	}
	err := d.engine.Model(&model.SysUser{}).
		Where("user_id = ?", id).
		Updates(values).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("delete from user_post where user_id = ?", id).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("delete from user_role where user_id = ?", id).Error; err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// 批量删除
func (d *Dao) DeleteUserByIds(ids []uint64, operater string) error {
	if len(ids) == 0 {
		return nil
	}
	tx := d.engine.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}
	values := map[string]interface{}{
		"del_flag":    "2",
		"update_by":   operater,
		"update_time": time.Now(),
	}
	err := d.engine.Model(&model.SysUser{}).
		Where("user_id IN (?)", ids).
		Updates(values).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("delete from user_post where user_id IN (?)", ids).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Exec("delete from user_role where user_id IN (?)", ids).Error; err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// UpdateUserStatus 更新用户状态
func (d *Dao) UpdateUserStatus(userId uint64, status string) error {
	return d.engine.Model(&model.SysUser{}).Where("user_id = ?", userId).Update("status", status).Error
}

// ResetUserPwd 重置用户密码
func (d *Dao) ResetUserPwd(userId uint64, password string) error {
	return d.engine.Model(&model.SysUser{}).Where("user_id = ?", userId).Update("password", password).Error
}

// GetUserList 获取用户列表
func (d *Dao) GetUserList(param *req.UserListRequest, fields []string, pageOption *app.PageOption) ([]*model.SysUser, int64, error) {
	var users []*model.SysUser
	query := d.engine.Model(&model.SysUser{}).Where("del_flag = ?", 0)

	if param.UserName != "" {
		query = query.Where("user_name like ?", "%"+param.UserName+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.PhoneNumber != "" {
		query = query.Where("phone_number like ?", "%"+param.PhoneNumber+"%")
	}
	if param.DeptId != 0 {
		query = query.Where("dept_id = ?", param.DeptId)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return nil, 0, err
	}
	err = query.Find(&users).Error
	return users, total, err
}

// AssignUserRole 分配用户角色
func (d *Dao) AssignUserRole(userId uint64, roleIds []uint64) error {
	// 先删除用户所有角色
	if err := d.engine.Where("user_id = ?", userId).Delete(&model.SysUserRole{}).Error; err != nil {
		return err
	}

	// 批量插入新的角色关联
	if len(roleIds) > 0 {
		userRoles := make([]*model.SysUserRole, 0, len(roleIds))
		for _, roleId := range roleIds {
			userRoles = append(userRoles, &model.SysUserRole{
				UserId: userId,
				RoleId: roleId,
			})
		}
		return d.engine.Create(&userRoles).Error
	}
	return nil
}

// AssignUserPost 分配用户岗位
func (d *Dao) AssignUserPost(userId uint64, postIds []uint64) error {
	// 先删除用户所有岗位
	if err := d.engine.Where("user_id = ?", userId).Delete(&model.SysUserPost{}).Error; err != nil {
		return err
	}

	// 批量插入新的岗位关联
	if len(postIds) > 0 {
		userPosts := make([]*model.SysUserPost, 0, len(postIds))
		for _, postId := range postIds {
			userPosts = append(userPosts, &model.SysUserPost{
				UserId: userId,
				PostId: postId,
			})
		}
		return d.engine.Create(&userPosts).Error
	}
	return nil
}

// GetUserPostsByUserIds 根据用户ID获取岗位
func (d *Dao) GetUserPostsByUserIds(userIds []uint64) (map[uint64][]*model.SysPost, error) {
	var results []struct {
		UserId uint64 `gorm:"column:user_id"`
		model.SysPost
	}

	if err := d.engine.Table("sys_user_post up").
		Select("up.user_id, p.*").
		Joins("LEFT JOIN sys_post p ON up.post_id = p.post_id").
		Where("up.user_id IN (?) AND p.status = ?", userIds, "0").
		Find(&results).Error; err != nil {
		return nil, err
	}

	userPostMap := make(map[uint64][]*model.SysPost)
	for _, result := range results {
		post := &model.SysPost{
			PostId:     result.PostId,
			PostCode:   result.PostCode,
			PostName:   result.PostName,
			PostSort:   result.PostSort,
			Status:     result.Status,
			CreateBy:   result.CreateBy,
			CreateTime: result.CreateTime,
			UpdateBy:   result.UpdateBy,
			UpdateTime: result.UpdateTime,
			Remark:     result.Remark,
		}
		userPostMap[result.UserId] = append(userPostMap[result.UserId], post)
	}
	return userPostMap, nil
}

// GetRoleIdsByUserId 根据用户ID获取角色ID
func (d *Dao) GetRoleIdsByUserId(userId uint64) ([]uint64, error) {
	roleIds := make([]uint64, 0, 8)
	err := d.engine.Model(&model.SysUserRole{}).Select("distinct role_id").
		Where("user_id = ?", userId).
		Pluck("role_id", &roleIds).Error
	if err != nil {
		return nil, err
	}
	return roleIds, err
}

// GetUserRolesByUserIds 根据用户ID获取角色
func (d *Dao) GetUserRolesByUserIds(userIds []uint64) (map[uint64][]*model.SysRole, error) {
	var results []struct {
		UserId uint64 `gorm:"column:user_id"`
		model.SysRole
	}

	if err := d.engine.Table("sys_user_role ur").
		Select("ur.user_id, r.*").
		Joins("LEFT JOIN sys_role r ON ur.role_id = r.role_id").
		Where("ur.user_id IN (?) AND r.status = ? AND r.del_flag = ?", userIds, "0", "0").
		Find(&results).Error; err != nil {
		return nil, err
	}

	userRoleMap := make(map[uint64][]*model.SysRole)
	for _, result := range results {
		role := &model.SysRole{
			RoleId:            result.RoleId,
			RoleName:          result.RoleName,
			RoleKey:           result.RoleKey,
			RoleSort:          result.RoleSort,
			DataScope:         result.DataScope,
			MenuCheckStrictly: result.MenuCheckStrictly,
			DeptCheckStrictly: result.DeptCheckStrictly,
			Status:            result.Status,
			DelFlag:           result.DelFlag,
			CreateBy:          result.CreateBy,
			CreateTime:        result.CreateTime,
			UpdateBy:          result.UpdateBy,
			UpdateTime:        result.UpdateTime,
			Remark:            result.Remark,
		}
		userRoleMap[result.UserId] = append(userRoleMap[result.UserId], role)
	}
	return userRoleMap, nil
}

// GetUserListByRoleId 根据角色id查询用户列表
func (d *Dao) GetUserListByRoleId(param *req.SelectRoleUserRequest, fields []string, pageOption *app.PageOption) (int64, []*model.SysUser, error) {
	var users []*model.SysUser
	query := d.engine.Table("sys_user u").
		Joins("left join sys_user_role ur on u.user_id = ur.user_id").
		Joins("left join sys_role r on r.role_id = ur.role_id").
		Where("u.del_flag = ? and r.role_id = ?", "0", param.RoleId)
	if param.UserName != "" {
		query = query.Where("u.user_name like ?", "%"+param.UserName+"%")
	}
	if param.PhoneNumber != "" {
		query = query.Where("u.phonenumber = ?", param.PhoneNumber)
	}
	if len(fields) > 0 {
		field := make([]string, 0)
		for _, v := range fields {
			if v == "user_id" {
				field = append(field, "distinct u."+v)
			} else {
				field = append(field, "u."+v)
			}
		}
		query = query.Select(field)
	}
	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return 0, nil, err
	}
	err = query.Find(&users).Error
	return total, users, err
}

// GetUserListByRoleIdExcludeUserIds 根据角色id查询用户列表(排除选择的用户)
func (d *Dao) GetUserListByRoleIdExcludeUserIds(param *req.SelectRoleUserRequest, fields []string, pageOption *app.PageOption) (int64, []*model.SysUser, error) {
	userIds, err := d.GetUserIdsByRoleId(param.RoleId)
	if err != nil {
		return 0, nil, err
	}
	var users []*model.SysUser
	query := d.engine.Table("sys_user u").
		Joins("left join sys_user_role ur on u.user_id = ur.user_id").
		Joins("left join sys_role r on r.role_id = ur.role_id").
		Where("u.del_flag = ? and (r.role_id != ? or r.role_id IS NULL)", "0", param.RoleId)

	// 只有当userIds不为空时才添加NOT IN条件
	if len(userIds) > 0 {
		query = query.Where("u.user_id not in (?)", userIds)
	}
	if param.UserName != "" {
		query = query.Where("u.user_name like ?", "%"+param.UserName+"%")
	}
	if param.PhoneNumber != "" {
		query = query.Where("u.phonenumber = ?", param.PhoneNumber)
	}
	if len(fields) > 0 {
		field := make([]string, 0)
		for _, v := range fields {
			if v == "user_id" {
				field = append(field, "distinct u."+v)
			} else {
				field = append(field, "u."+v)
			}
		}
		query = query.Select(field)
	}
	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return 0, nil, err
	}
	err = query.Find(&users).Error
	return total, users, err
}

// DeleteByRoleAndUser 单例删除用户角色关系
func (d *Dao) DeleteByRoleAndUser(userId, roleId uint64) error {
	return d.engine.Where("user_id = ? and role_id = ?", userId, roleId).
		Delete(&model.SysUserRole{}).Error
}

// DeleteByRoleAndUsers 批量删除用户角色关系
func (d *Dao) DeleteByRoleAndUsers(roleId uint64, userIds []uint64) error {
	return d.engine.Where("role_id = ? and user_id in (?)", roleId, userIds).
		Delete(&model.SysUserRole{}).Error
}
