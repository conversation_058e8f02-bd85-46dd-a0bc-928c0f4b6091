package dao

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app/req"
	"medlink/pkg/tools/convert"
	"time"

	"gorm.io/gorm"
)

// CreateMenu 创建菜单
func (d *Dao) CreateMenu(param *req.CreateMenuRequest) error {
	now := time.Now()
	menu := &model.SysMenu{
		MenuName:   param.MenuName,
		ParentId:   convert.StrTo(param.ParentId).MustUInt64(),
		OrderNum:   convert.StrTo(param.OrderNum).MustInt(),
		Path:       param.Path,
		Component:  param.Component,
		Query:      param.Query,
		RouteName:  param.RouteName,
		IsFrame:    convert.StrTo(param.IsFrame).MustInt(),
		IsCache:    convert.StrTo(param.IsCache).MustInt(),
		MenuType:   param.MenuType,
		Visible:    param.Visible,
		Status:     param.Status,
		Perms:      param.Perms,
		Icon:       param.Icon,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
		Remark:     param.Remark,
	}
	return d.engine.Create(menu).Error
}

// UpdateMenu 更新菜单
func (d *Dao) UpdateMenu(id uint64, values map[string]interface{}) error {
	return d.engine.Model(&model.SysMenu{}).
		Where("menu_id = ?", id).
		Updates(values).Error
}

// DeleteMenu 删除菜单
func (d *Dao) DeleteMenu(menuId uint64) error {
	return d.engine.Delete(&model.SysMenu{}, "menu_id = ?", menuId).Error
}

// GetMenuById 根据ID获取菜单
func (d *Dao) GetMenuById(menuId uint64, fields []string) (*model.SysMenu, error) {
	var menu model.SysMenu
	query := d.engine.Where("menu_id = ?", menuId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&menu).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("菜单不存在")
	}
	return &menu, err
}

// GetMenuByMenuName 根据菜单名称获取菜单
func (d *Dao) GetMenuByMenuName(menuName string, fields []string) (*model.SysMenu, error) {
	var menu model.SysMenu
	query := d.engine.Where("menu_name = ?", menuName)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&menu).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("菜单不存在")
	}
	return &menu, err
}

// GetMenuList 获取菜单列表
func (d *Dao) GetMenuList(param *req.MenuListRequest, fields []string) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	query := d.engine.Model(&model.SysMenu{})
	if param.MenuId != 0 {
		query = query.Where("menu_id = ?", param.MenuId)
	}
	if param.ParentId != 0 {
		query = query.Where("parent_id = ?", param.ParentId)
	}
	if param.MenuName != "" {
		query = query.Where("menu_name like ?", "%"+param.MenuName+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Order("parent_id, order_num").Find(&menus).Error
	return menus, err
}

// GetMenuListByParentId 根据父ID获取菜单列表
func (d *Dao) GetMenuListByParentId(parentId uint64, fields []string) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	query := d.engine.Where("parent_id = ? AND status = ?", parentId, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("order_num").Find(&menus).Error
	return menus, err
}

// GetMenusByRoleId 根据角色ID查询菜单列表
func (d *Dao) GetMenusByRoleId(roleId uint64, fields []string) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	query := d.engine.Table("sys_menu m").
		Joins("left join sys_role_menu rm on m.menu_id = rm.menu_id").
		Where("rm.role_id = ? AND m.status = ?", roleId, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("m.parent_id, m.order_num").Find(&menus).Error
	return menus, err
}

// HasChildByMenuId 判断是否有子菜单
func (d *Dao) HasChildByMenuId(menuId uint64) (bool, error) {
	var count int64
	err := d.engine.Model(&model.SysMenu{}).Where("parent_id = ? AND status = ?", menuId, "0").Count(&count).Error
	return count > 0, err
}

// GetMenus 获取所有菜单
func (d *Dao) GetMenus(menuType []string, fields []string) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	query := d.engine.Where("status =?", "0")
	if len(menuType) > 0 {
		query = query.Where("menu_type in (?)", menuType)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Order("parent_id, order_num").Find(&menus).Error
	return menus, err
}

// GetMenusByUserId 获取用户菜单
func (d *Dao) GetMenusByUserId(userId uint64, menuType []string, fields []string) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	query := d.engine.Table("sys_menu m").
		Joins("left join sys_role_menu rm on m.menu_id = rm.menu_id").
		Joins("left join sys_user_role ur on rm.role_id = ur.role_id").
		Where("ur.user_id =? AND m.status =?", userId, "0")
	if len(menuType) > 0 {
		query = query.Where("m.menu_type in (?)", menuType)
	}
	if len(fields) > 0 {
		var selectFields []string
		for _, field := range fields {
			selectFields = append(selectFields, "m."+field)
		}
		query = query.Select(selectFields)
	}
	err := query.Order("m.parent_id, m.order_num").Find(&menus).Error
	return menus, err
}
