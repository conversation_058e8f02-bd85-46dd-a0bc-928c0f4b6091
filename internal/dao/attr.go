package dao

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/tools/convert"

	"gorm.io/gorm"
)

// 获取属性定义列表
func (d *Dao) GetAttrList(param *req.AttrListRequest, fields []string, pageOption *app.PageOption) ([]*model.SysAttributeDefinitions, int64, error) {
	var attrs []*model.SysAttributeDefinitions
	query := d.engine.Model(&model.SysAttributeDefinitions{})
	// 添加查询条件
	if param.EntityType != "" {
		query = query.Where("entity_type = ?", param.EntityType)
	}
	if param.AttrCode != "" {
		query = query.Where("attr_code LIKE ?", "%"+param.AttrCode+"%")
	}
	if param.AttrName != "" {
		query = query.Where("attr_name LIKE ?", "%"+param.AttrName+"%")
	}
	if param.FieldType != "" {
		query = query.Where("field_type = ?", param.FieldType)
	}
	if param.DataType != "" {
		query = query.Where("data_type = ?", param.DataType)
	}
	if param.IsRequired != "" {
		isRequired := 0
		if param.IsRequired == "1" {
			isRequired = 1
		}
		query = query.Where("is_required = ?", isRequired)
	}

	// 默认按排序字段和ID排序
	if pageOption.OrderBy == "" {
		pageOption.OrderBy = "sort_order ASC, id ASC"
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return nil, 0, err
	}

	err = query.Find(&attrs).Error
	return attrs, total, err
}

// 根据实体类型和属性代码获取属性定义
func (d *Dao) GetAttrByCode(entityType, attrCode string, fields []string) (*model.SysAttributeDefinitions, error) {
	var attr model.SysAttributeDefinitions
	query := d.engine.Where("entity_type = ? AND attr_code = ?", entityType, attrCode)

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.First(&attr).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("属性定义不存在")
	}
	return &attr, err
}

// 根据ID获取属性定义
func (d *Dao) GetAttrById(id uint64, fields []string) (*model.SysAttributeDefinitions, error) {
	var attr model.SysAttributeDefinitions
	query := d.engine.Where("id = ?", id)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	if err := query.First(&attr).Error; err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("属性定义不存在")
	}
	return &attr, nil
}

// 创建属性定义
func (d *Dao) CreateAttr(param *req.CreateAttrRequest) (*model.SysAttributeDefinitions, error) {
	isRequired := 0
	if param.IsRequired == "1" {
		isRequired = 1
	}
	attr := model.SysAttributeDefinitions{
		EntityType: param.EntityType,
		AttrCode:   param.AttrCode,
		AttrName:   param.AttrName,
		FieldType:  param.FieldType,
		DataType:   param.DataType,
		IsRequired: isRequired,
		Config:     param.Config,
		SortOrder:  convert.StrTo(param.SortOrder).MustInt(),
	}
	if err := d.engine.Create(&attr).Error; err != nil {
		return nil, err
	}

	return &attr, nil
}

// 更新属性定义
func (d *Dao) UpdateAttr(id uint64, param *req.UpdateAttrRequest) error {
	updates := make(map[string]interface{})
	if param.EntityType != "" {
		updates["entity_type"] = param.EntityType
	}
	if param.AttrCode != "" {
		updates["attr_code"] = param.AttrCode
	}
	if param.AttrName != "" {
		updates["attr_name"] = param.AttrName
	}
	if param.FieldType != "" {
		updates["field_type"] = param.FieldType
	}
	if param.DataType != "" {
		updates["data_type"] = param.DataType
	}
	if param.IsRequired != "" {
		updates["is_required"] = convert.StrTo(param.IsRequired).MustInt()
	}
	if param.Config != "" {
		updates["config"] = param.Config
	}
	if param.SortOrder != "" {
		updates["sort_order"] = convert.StrTo(param.SortOrder).MustInt()
	}
	if len(updates) == 0 {
		return errors.New("没有需要更新的字段")
	}
	return d.engine.Model(&model.SysAttributeDefinitions{}).Where("id = ?", id).Updates(updates).Error
}
