package dao

import (
	"medlink/internal/model"
	"time"
)

// 创建登录日志
func (d *Dao) CreateLoginInfo(loginInfo *model.SysLogininfor) error {
	return d.engine.Create(loginInfo).Error
}

// 创建在线用户记录
func (d *Dao) CreateUserOnline(userOnline *model.SysUserOnline) error {
	return d.engine.Create(userOnline).Error
}

// 更新在线用户记录
func (d *Dao) UpdateUserOnline(sessionId string, values map[string]interface{}) error {
	return d.engine.Model(&model.SysUserOnline{}).
		Where("sessionId = ?", sessionId).
		Updates(values).Error
}

// 删除在线用户记录
func (d *Dao) DeleteUserOnline(sessionId string) error {
	return d.engine.Where("sessionId = ?", sessionId).
		Delete(&model.SysUserOnline{}).Error
}

// 根据会话ID获取在线用户记录
func (d *Dao) GetUserOnlineBySessionId(sessionId string) (*model.SysUserOnline, error) {
	var userOnline model.SysUserOnline
	err := d.engine.Where("sessionId = ?", sessionId).First(&userOnline).Error
	return &userOnline, err
}

// 清理过期的在线用户记录
func (d *Dao) CleanExpiredUserOnline() error {
	now := time.Now()
	return d.engine.Where("DATE_ADD(last_access_time, INTERVAL expire_time MINUTE) < ?", now).
		Delete(&model.SysUserOnline{}).Error
}

// 创建操作日志
func (d *Dao) CreateOperLog(operLog *model.SysOperLog) error {
	return d.engine.Create(operLog).Error
}

// 获取在线用户列表
func (d *Dao) GetUserOnlineList(status string) ([]*model.SysUserOnline, error) {
	var userOnlines []*model.SysUserOnline
	query := d.engine.Model(&model.SysUserOnline{})

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Order("start_timestamp DESC").Find(&userOnlines).Error
	return userOnlines, err
}

// 获取登录日志列表
func (d *Dao) GetLoginInfoList(loginName, status string, limit int) ([]*model.SysLogininfor, error) {
	var loginInfos []*model.SysLogininfor
	query := d.engine.Model(&model.SysLogininfor{})

	if loginName != "" {
		query = query.Where("login_name LIKE ?", "%"+loginName+"%")
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Order("login_time DESC").Find(&loginInfos).Error
	return loginInfos, err
}

// 获取操作日志列表
func (d *Dao) GetOperLogList(operName, title string, businessType, status int, limit int) ([]*model.SysOperLog, error) {
	var operLogs []*model.SysOperLog
	query := d.engine.Model(&model.SysOperLog{})

	if operName != "" {
		query = query.Where("oper_name LIKE ?", "%"+operName+"%")
	}

	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	if businessType >= 0 {
		query = query.Where("business_type = ?", businessType)
	}

	if status >= 0 {
		query = query.Where("status = ?", status)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Order("oper_time DESC").Find(&operLogs).Error
	return operLogs, err
}
