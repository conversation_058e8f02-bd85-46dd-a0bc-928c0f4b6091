package dao

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/tools/convert"
	"time"

	"gorm.io/gorm"
)

// CreateDictData 创建字典数据
func (d *Dao) CreateDictData(param *req.CreateDictDataRequest) error {
	now := time.Now()
	dictData := &model.SysDictData{
		DictSort:   convert.StrTo(param.DictSort).MustInt(),
		DictLabel:  param.DictLabel,
		DictValue:  param.DictValue,
		DictType:   param.DictType,
		CssClass:   param.CssClass,
		ListClass:  param.ListClass,
		Status:     param.Status,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
		Remark:     param.Remark,
	}
	return d.engine.Create(dictData).Error
}

// UpdateDictData 更新字典数据
func (d *Dao) UpdateDictData(id uint64, values map[string]interface{}) error {
	return d.engine.Model(&model.SysDictData{}).Where("dict_code = ?", id).Updates(values).Error
}

// DeleteDictData 删除字典数据
func (d *Dao) DeleteDictData(dictCode uint64) error {
	return d.engine.Delete(&model.SysDictData{}, "dict_code = ?", dictCode).Error
}

// DeleteDictDatas 批量删除字典数据
func (d *Dao) DeleteDictDatas(dictCodes []uint64) error {
	if len(dictCodes) == 0 {
		return nil
	}
	if len(dictCodes) == 1 {
		return d.DeleteDictData(dictCodes[0])
	}
	return d.engine.Where("dict_code in (?)", dictCodes).Delete(&model.SysDictData{}).Error
}

// GetDictDataById 根据ID获取字典数据
func (d *Dao) GetDictDataById(dictCode uint64, fields []string) (*model.SysDictData, error) {
	var dictData model.SysDictData
	query := d.engine.Where("dict_code = ?", dictCode)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictData).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("字典数据不存在")
	}
	return &dictData, err
}

// GetDictDataByDictLabel 根据字典标签获取字典数据
func (d *Dao) GetDictDataByDictLabel(dictType, dictLabel string, fields []string) (*model.SysDictData, error) {
	var dictData model.SysDictData
	query := d.engine.Where("dict_type = ? and dict_label = ?", dictType, dictLabel)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictData).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("字典数据不存在")
	}
	return &dictData, err
}

// GetDictDataByDictValue 根据字典值获取字典数据
func (d *Dao) GetDictDataByDictValue(dictType, dictValue string, fields []string) (*model.SysDictData, error) {
	var dictData model.SysDictData
	query := d.engine.Where("dict_type = ? and dict_value = ?", dictType, dictValue)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictData).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, errors.New("字典数据不存在")
	}
	return &dictData, err
}

// GetDictDataList 获取字典数据列表
func (d *Dao) GetDictDataList(param *req.DictDataListRequest, fields []string, pageOption *app.PageOption) ([]*model.SysDictData, int64, error) {
	var dictDataList []*model.SysDictData
	query := d.engine.Model(&model.SysDictData{})
	if param.DictType != "" {
		query = query.Where("dict_type = ?", param.DictType)
	}
	if param.DictLabel != "" {
		query = query.Where("dict_label like ?", "%"+param.DictLabel+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	// 如果指定了字段，则只查询指定字段
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return nil, 0, err
	}
	err = query.Find(&dictDataList).Error
	return dictDataList, total, err
}

// GetDictDataByType 根据字典类型查询字典数据
func (d *Dao) GetDictDataByType(dictType string, fields []string) ([]*model.SysDictData, error) {
	var dictDataList []*model.SysDictData
	query := d.engine.Where("dict_type = ? AND status = ?", dictType, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("dict_sort").Find(&dictDataList).Error
	return dictDataList, err
}
