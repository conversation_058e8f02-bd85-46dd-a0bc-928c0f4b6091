package routers

import (
	"medlink/internal/routers/api"
	"medlink/internal/routers/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAPIRoutes(r *gin.Engine) {
	authController := api.NewAuthController()
	// 获取验证码
	r.GET("/captchaImage", authController.CaptchaImage)
	// 登录方法
	r.POST("/login", authController.Login)
	// 获取用户详细信息
	r.GET("/getInfo", middleware.JWT(), authController.GetInfo)
	// 获取路由
	r.GET("/getRouters", middleware.JWT(), authController.GetRouters)
	// 退出方法
	r.POST("/logout", authController.Logout)

	userController := api.NewUserController()
	userGroup := r.Group("/user").Use(middleware.JWT())
	{
		//查询用户列表
		userGroup.GET("/list", middleware.CheckPermission("system:user:list"), userController.GetList)
		//查询用户详细
		userGroup.GET("/:user_id", middleware.CheckPermission("system:user:query"), userController.GetDetailByUserId)
		//新增用户
		userGroup.POST("", middleware.CheckPermission("system:user:add"), userController.CreateUser)
		//修改用户
		userGroup.PUT("", middleware.CheckPermission("system:user:edit"), userController.UpdateUser)
		//删除用户
		userGroup.DELETE("/:user_id", middleware.CheckPermission("system:user:remove"), userController.DeleteUser)
		//用户密码重置
		userGroup.PUT("/resetPwd", middleware.CheckPermission("system:user:resetPwd"), userController.ResetPassword)
		//用户状态修改
		userGroup.PUT("/changeStatus", middleware.CheckPermission("system:user:edit"), userController.ChangeStatus)
		//查询用户个人信息
		userGroup.GET("/profile", authController.GetProfileByLoginUser)
		//修改用户个人信息
		userGroup.PUT("/profile", authController.UpdateProfileByLoginUser)
		//用户密码重置
		userGroup.PUT("/profile/updatePwd", authController.UpdatePasswordByLoginUser)
		//用户头像上传
		userGroup.POST("/profile/avatar", authController.UploadAvatarByLoginUser)
		//查询授权角色
		userGroup.GET("/authRole/:user_id", middleware.CheckPermission("system:user:query"), userController.GetAuthRoleByUserId)
		//保存授权角色
		userGroup.PUT("/authRole", middleware.CheckPermission("system:user:edit"), userController.SaveAuthRole)
		//查询部门下拉树结构
		userGroup.GET("/deptTree", middleware.CheckPermission("system:user:list"), userController.GetDeptTree)
	}

	deptController := api.NewDeptController()
	deptGroup := r.Group("/dept").Use(middleware.JWT())
	{
		// 查询部门列表
		deptGroup.GET("/list", middleware.CheckPermission("system:dept:list"), deptController.GetList)
		// 查询部门列表（排除节点）
		deptGroup.GET("/list/exclude/:dept_id", middleware.CheckPermission("system:dept:list"), deptController.GetListExcludeChildren)
		// 查询部门详细
		deptGroup.GET("/:dept_id", middleware.CheckPermission("system:dept:query"), deptController.GetDetailByDeptId)
		// 新增部门
		deptGroup.POST("", middleware.CheckPermission("system:dept:add"), deptController.CreateDept)
		// 修改部门
		deptGroup.PUT("", middleware.CheckPermission("system:dept:edit"), deptController.UpdateDept)
		// 删除部门
		deptGroup.DELETE("/:dept_id", middleware.CheckPermission("system:dept:remove"), deptController.DeleteDept)
	}

	postController := api.NewPostController()
	postGroup := r.Group("/post").Use(middleware.JWT())
	{
		//查询岗位列表
		postGroup.GET("/list", middleware.CheckPermission("system:post:list"), postController.GetList)
		//查询岗位详情
		postGroup.GET("/:post_id", middleware.CheckPermission("system:post:query"), postController.GetDetailByPostId)
		//新增岗位
		postGroup.POST("", middleware.CheckPermission("system:post:add"), postController.CreatePost)
		//修改岗位
		postGroup.PUT("", middleware.CheckPermission("system:post:edit"), postController.UpdatePost)
		//删除岗位
		postGroup.DELETE("/:post_id", middleware.CheckPermission("system:post:remove"), postController.DeletePost)
	}

	roleController := api.NewRoleController()
	roleGroup := r.Group("/role").Use(middleware.JWT())
	{
		//查询角色列表
		roleGroup.GET("/list", middleware.CheckPermission("system:role:list"), roleController.GetList)
		//查询角色详细
		roleGroup.GET("/:role_id", middleware.CheckPermission("system:role:query"), roleController.GetDetailByRoleId)
		//新增角色
		roleGroup.POST("", middleware.CheckPermission("system:role:add"), roleController.CreateRole)
		//修改角色
		roleGroup.PUT("", middleware.CheckPermission("system:role:edit"), roleController.UpdateRole)
		//角色数据权限
		roleGroup.PUT("/dataScope", middleware.CheckPermission("system:role:edit"), roleController.UpdateDataScope)
		//角色状态修改
		roleGroup.PUT("/changeStatus", middleware.CheckPermission("system:role:edit"), roleController.ChangeStatus)
		//删除角色
		roleGroup.DELETE("/:role_id", middleware.CheckPermission("system:role:remove"), roleController.DeleteRole)
		//查询角色已授权用户列表
		roleGroup.GET("/authUser/allocatedList", middleware.CheckPermission("system:role:list"), roleController.GetAllocatedList)
		//查询角色未授权用户列表
		roleGroup.GET("/authUser/unallocatedList", middleware.CheckPermission("system:role:list"), roleController.GetUnallocatedList)
		//取消用户授权角色
		roleGroup.PUT("/authUser/cancel", middleware.CheckPermission("system:role:edit"), roleController.CancelAuthUser)
		//批量取消用户授权角色
		roleGroup.PUT("/authUser/cancelAll", middleware.CheckPermission("system:role:edit"), roleController.CancelAllAuthUser)
		//授权用户选择
		roleGroup.PUT("/authUser/selectAll", middleware.CheckPermission("system:role:edit"), roleController.SelectAllAuthUser)
		//根据角色ID查询部门树结构
		roleGroup.GET("/deptTree/:role_id", middleware.CheckPermission("system:role:query"), roleController.GetDeptTreeByRoleId)
	}

	menuController := api.NewMenuController()
	menuGroup := r.Group("/menu").Use(middleware.JWT())
	{
		//查询菜单列表
		menuGroup.GET("/list", middleware.CheckPermission("system:menu:list"), menuController.GetList)
		//查询菜单详细
		menuGroup.GET("/:menu_id", middleware.CheckPermission("system:menu:query"), menuController.GetDetailByMenuId)
		//查询菜单下拉树结构
		menuGroup.GET("/treeselect", menuController.GetTreeSelect)
		//根据角色ID查询菜单下拉树结构
		menuGroup.GET("/roleMenuTreeselect/:role_id", menuController.GetRoleMenuTreeSelect)
		//新增菜单
		menuGroup.POST("", middleware.CheckPermission("system:menu:add"), menuController.CreateMenu)
		//修改菜单
		menuGroup.PUT("", middleware.CheckPermission("system:menu:edit"), menuController.UpdateMenu)
		//删除菜单
		menuGroup.DELETE("/:menu_id", middleware.CheckPermission("system:menu:remove"), menuController.DeleteMenu)
	}

	dictController := api.NewDictController()
	dictGroup := r.Group("/dict").Use(middleware.JWT())
	{
		//查询字典列表
		dictGroup.GET("/data/list", middleware.CheckPermission("system:dict:list"), dictController.GetDataList)
		//查询字典详细
		dictGroup.GET("/data/:dict_code", middleware.CheckPermission("system:dict:query"), dictController.GetDataDetailByDictCode)
		//根据字典类型查询字典数据信息
		dictGroup.GET("/data/type/:dict_type", dictController.GetDictDataByType)
		//新增字典类型
		dictGroup.POST("/data", middleware.CheckPermission("system:dict:add"), dictController.CreateDictData)
		//修改保存字典类型
		dictGroup.PUT("/data", middleware.CheckPermission("system:dict:edit"), dictController.UpdateDictData)
		//删除字典类型
		dictGroup.DELETE("/data/:dict_code", middleware.CheckPermission("system:dict:remove"), dictController.DeleteDictData)

		//查询字典类型列表
		dictGroup.GET("/type/list", middleware.CheckPermission("system:dict:list"), dictController.GetTypeList)
		//查询字典类型详细
		dictGroup.GET("/type/:dict_id", middleware.CheckPermission("system:dict:query"), dictController.GetTypeDetailByDictId)
		//新增字典类型
		dictGroup.POST("/type", middleware.CheckPermission("system:dict:add"), dictController.CreateDictType)
		//修改字典类型
		dictGroup.PUT("/type", middleware.CheckPermission("system:dict:edit"), dictController.UpdateDictType)
		//删除字典类型
		dictGroup.DELETE("/type/:dict_id", middleware.CheckPermission("system:dict:remove"), dictController.DeleteDictType)

		//获取字典选择框列表
		dictGroup.GET("/type/optionselect", dictController.GetDictTypeOptionSelect)
	}

	// 属性定义管理路由
	attrDefController := api.NewAttributeDefinitionController()
	attrDefGroup := r.Group("/attribute-definitions").Use(middleware.JWT())
	{
		// 查询属性定义列表
		attrDefGroup.GET("/list", middleware.CheckPermission("system:attr:list"), attrDefController.GetList)
		// 查询属性定义详细
		attrDefGroup.GET("/:id", middleware.CheckPermission("system:attr:query"), attrDefController.GetDetail)
		// 新增属性定义
		attrDefGroup.POST("", middleware.CheckPermission("system:attr:add"), attrDefController.Create)
		// 修改属性定义
		attrDefGroup.PUT("", middleware.CheckPermission("system:attr:edit"), attrDefController.Update)
		// 删除属性定义
		attrDefGroup.DELETE("/:id", middleware.CheckPermission("system:attr:remove"), attrDefController.Delete)
		// 批量删除属性定义
		attrDefGroup.POST("/batch-delete", middleware.CheckPermission("system:attr:remove"), attrDefController.BatchDelete)

		// 根据实体类型获取属性定义
		attrDefGroup.GET("/entity", attrDefController.GetByEntity)
		// 获取所有实体类型
		attrDefGroup.GET("/entity-types", attrDefController.GetEntityTypes)

		// 验证字段配置
		attrDefGroup.POST("/validate-config", attrDefController.ValidateConfig)
		// 复制属性定义
		attrDefGroup.POST("/copy", middleware.CheckPermission("system:attr:add"), attrDefController.CopyDefinitions)
		// 批量更新排序
		attrDefGroup.PUT("/sort-order", middleware.CheckPermission("system:attr:edit"), attrDefController.BatchUpdateSortOrder)

		// 获取统计信息
		attrDefGroup.GET("/stats", middleware.CheckPermission("system:attr:list"), attrDefController.GetStats)
		// 获取字段类型配置模式
		attrDefGroup.GET("/field-type-schemas", attrDefController.GetFieldTypeSchemas)
	}

	// 动态实体管理路由
	dynamicEntityController := api.NewDynamicEntityController()
	dynamicEntityGroup := r.Group("/dynamic-entities").Use(middleware.JWT())
	{
		// 查询动态实体列表
		dynamicEntityGroup.GET("/list", middleware.CheckPermission("system:entity:list"), dynamicEntityController.GetList)
		// 查询动态实体详细
		dynamicEntityGroup.GET("/:id", middleware.CheckPermission("system:entity:query"), dynamicEntityController.GetDetail)
		// 新增动态实体
		dynamicEntityGroup.POST("", middleware.CheckPermission("system:entity:add"), dynamicEntityController.Create)
		// 修改动态实体
		dynamicEntityGroup.PUT("", middleware.CheckPermission("system:entity:edit"), dynamicEntityController.Update)
		// 删除动态实体
		dynamicEntityGroup.DELETE("/:id", middleware.CheckPermission("system:entity:remove"), dynamicEntityController.Delete)
		// 批量删除动态实体
		dynamicEntityGroup.POST("/batch-delete", middleware.CheckPermission("system:entity:remove"), dynamicEntityController.BatchDelete)

		// 获取动态表单结构
		dynamicEntityGroup.GET("/form", dynamicEntityController.GetForm)
		// 验证动态实体数据
		dynamicEntityGroup.POST("/validate", dynamicEntityController.Validate)
	}
}
