package middleware

import (
	"encoding/json"
	"medlink/global"
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/errcode"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/utils"
)

//	权限检查中间件
//
// permission: 需要检查的权限标识，如 "system:user:list"
func CheckPermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果权限标识为空，直接通过
		if permission == "" {
			c.Next()
			return
		}

		// 获取用户ID（JWT中间件已经设置）
		userIdInterface, exists := c.Get("user_id")
		if !exists {
			utils.Error(c, errcode.UnauthorizedTokenError, utils.WithMessage("用户未登录"))
			c.Abort()
			return
		}

		userId, ok := userIdInterface.(uint64)
		if !ok || userId == 0 {
			utils.Error(c, errcode.UnauthorizedTokenError, utils.WithMessage("用户ID无效"))
			c.Abort()
			return
		}

		// 检查用户是否有指定权限
		hasPermission, err := checkUserPermission(c, userId, permission)
		if err != nil {
			utils.Error(c, errcode.ErrorPermissionCheck, utils.WithMessage("权限检查失败: "+err.Error()))
			c.Abort()
			return
		}

		if !hasPermission {
			utils.Error(c, errcode.ErrorPermissionDenied, utils.WithMessage("权限不足"))
			c.Abort()
			return
		}

		c.Next()
	}
}

// 检查用户是否具有指定权限
func checkUserPermission(c *gin.Context, userId uint64, permission string) (bool, error) {
	// 先从Redis缓存中获取用户权限
	permissions, err := getUserPermissionsFromCache(c, userId)
	if err == nil && permissions != nil {
		return hasPermissionInList(permissions, permission), nil
	}

	// 缓存中没有，从数据库获取
	permissions, err = getUserPermissionsFromDB(c, userId)
	if err != nil {
		return false, err
	}

	// 将权限列表缓存到Redis,缓存失败不影响权限检查，只记录错误
	_ = cacheUserPermissions(c, userId, permissions)

	return hasPermissionInList(permissions, permission), nil
}

// 从Redis缓存获取用户权限
func getUserPermissionsFromCache(c *gin.Context, userId uint64) ([]string, error) {
	cacheKey := app.UserPermissionsKey(userId)
	permissionsJson, err := global.App.Redis.Get(c, cacheKey)
	if err != nil {
		return nil, err
	}

	if permissionsJson == "" {
		return nil, nil
	}

	var permissions []string
	if err := json.Unmarshal([]byte(permissionsJson), &permissions); err != nil {
		return nil, err
	}

	return permissions, nil
}

// 从数据库获取用户权限
func getUserPermissionsFromDB(c *gin.Context, userId uint64) ([]string, error) {
	if userId == 1 {
		return []string{"*:*:*"}, nil
	}
	svc := service.New(c.Request.Context())

	// 获取用户的角色ID列表
	roleIds, err := svc.GetRoleIdsByUserId(userId)
	if err != nil {
		return nil, err
	}

	if len(roleIds) == 0 {
		return []string{}, nil
	}

	// 获取角色关联的菜单权限
	menuList, err := svc.GetRoleMenusByRoleIds(roleIds)
	if err != nil {
		return nil, err
	}

	// 提取权限标识
	permissionSet := make(map[string]bool)
	for _, menus := range menuList {
		for _, menu := range menus {
			if menu.Perms != "" {
				// 权限标识可能包含多个，用逗号分隔
				perms := strings.Split(menu.Perms, ",")
				for _, perm := range perms {
					perm = strings.TrimSpace(perm)
					if perm != "" {
						permissionSet[perm] = true
					}
				}
			}
		}
	}

	// 转换为切片
	permissions := make([]string, 0, len(permissionSet))
	for perm := range permissionSet {
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

// 缓存用户权限到Redis
func cacheUserPermissions(c *gin.Context, userId uint64, permissions []string) error {
	cacheKey := app.UserPermissionsKey(userId)
	permissionsJson, err := json.Marshal(permissions)
	if err != nil {
		return err
	}

	// 缓存30分钟
	expire := 30 * time.Minute
	return global.App.Redis.Set(c, cacheKey, string(permissionsJson), expire)
}

// 检查权限列表中是否包含指定权限
func hasPermissionInList(permissions []string, targetPermission string) bool {
	for _, perm := range permissions {
		if perm == targetPermission {
			return true
		}
		// 支持超级管理员权限
		if perm == "*:*:*" {
			return true
		}
		// 支持通配符权限，如 "system:user:*" 可以匹配 "system:user:list"
		if strings.HasSuffix(perm, ":*") {
			prefix := strings.TrimSuffix(perm, "*")
			if strings.HasPrefix(targetPermission, prefix) {
				return true
			}
		}
	}
	return false
}
