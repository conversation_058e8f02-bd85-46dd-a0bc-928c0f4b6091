package middleware

import (
	"medlink/global"
	"medlink/pkg/app"
	"medlink/pkg/errcode"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/utils"
)

func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Get<PERSON>eader("auth-service")
		if token == "" {
			utils.Error(c, errcode.UnauthorizedTokenError)
			c.Abort()
			return
		}

		claims, err := app.ParseToken(token)
		if err != nil {
			utils.Error(c, errcode.UnauthorizedTokenError)
			c.Abort()
			return
		} else if time.Now().Unix() > claims.ExpiresAt {
			utils.Error(c, errcode.UnauthorizedTokenTimeout)
			c.Abort()
			return
		}

		// 验证Redis中的token
		cachedToken, err := global.App.Redis.Get(c, app.UserTokenKey(claims.UserId))
		if err != nil || cachedToken != token {
			utils.Error(c, errcode.UnauthorizedTokenError)
			c.Abort()
			return
		}

		c.Set("token", token)
		c.Set("user_id", claims.UserId)
		c.Set("username", claims.UserName)
		c.Next()
	}
}
