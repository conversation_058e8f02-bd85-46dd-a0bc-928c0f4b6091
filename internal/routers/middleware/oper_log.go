package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"medlink/pkg/event"
	"time"

	"github.com/gin-gonic/gin"
)

// OperLogConfig 操作日志配置
type OperLogConfig struct {
	Title          string // 模块标题
	BusinessType   int    // 业务类型
	IsSaveReqData  bool   // 是否保存请求数据
	IsSaveRespData bool   // 是否保存响应数据
}

// OperLog 操作日志中间件
func OperLog(config OperLogConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// 获取用户信息
		operName := ""
		deptName := ""
		if userIdInterface, exists := c.Get("user_id"); exists {
			if userId, ok := userIdInterface.(uint64); ok && userId > 0 {
				// 这里可以根据用户ID获取用户名和部门名
				// 为了简化，暂时使用用户ID作为操作人员
				if loginNameInterface, exists := c.Get("username"); exists {
					if loginName, ok := loginNameInterface.(string); ok {
						operName = loginName
					}
				}
				if deptNameInterface, exists := c.Get("dept_name"); exists {
					if dName, ok := deptNameInterface.(string); ok {
						deptName = dName
					}
				}
			}
		}

		// 保存请求体（如果需要）
		var requestBody []byte
		if config.IsSaveReqData && (c.Request.Method == "POST" || c.Request.Method == "PUT") {
			if c.Request.Body != nil {
				requestBody, _ = io.ReadAll(c.Request.Body)
				// 重新设置请求体，以便后续处理
				c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
			}
		}

		// 创建响应写入器包装器
		var responseBody *bytes.Buffer
		var writer gin.ResponseWriter = c.Writer
		if config.IsSaveRespData {
			responseBody = &bytes.Buffer{}
			writer = &responseWriter{
				ResponseWriter: c.Writer,
				body:           responseBody,
			}
			c.Writer = writer
		}

		// 执行请求
		c.Next()

		// 构建结果数据
		result := make(map[string]interface{})

		// 添加请求参数
		if config.IsSaveReqData {
			// 添加URL参数
			if len(c.Request.URL.Query()) > 0 {
				queryParams := make(map[string]interface{})
				for key, values := range c.Request.URL.Query() {
					if len(values) > 0 {
						queryParams[key] = values[0]
					}
				}
				if len(queryParams) > 0 {
					result["query"] = queryParams
				}
			}

			// 添加路径参数
			if len(c.Params) > 0 {
				pathParams := make(map[string]interface{})
				for _, param := range c.Params {
					pathParams[param.Key] = param.Value
				}
				result["path"] = pathParams
			}

			// 添加请求体参数
			if len(requestBody) > 0 {
				var reqData interface{}
				if err := json.Unmarshal(requestBody, &reqData); err == nil {
					result["body"] = reqData
				}
			}
		}

		// 添加响应数据并检查业务状态
		var businessErr error
		if config.IsSaveRespData && responseBody != nil {
			var respData interface{}
			if unmarshalErr := json.Unmarshal(responseBody.Bytes(), &respData); unmarshalErr == nil {
				result["response"] = respData

				// 检查响应中的业务状态码
				if respMap, ok := respData.(map[string]interface{}); ok {
					if code, exists := respMap["code"]; exists {
						// 检查code是否不等于1
						if codeFloat, ok := code.(float64); ok && codeFloat != 1 {
							// 获取msg作为错误信息
							if msg, msgExists := respMap["msg"]; msgExists {
								if msgStr, ok := msg.(string); ok {
									businessErr = fmt.Errorf("%s", msgStr)
								}
							}
						} else if codeInt, ok := code.(int); ok && codeInt != 1 {
							// 处理整数类型的code
							if msg, msgExists := respMap["msg"]; msgExists {
								if msgStr, ok := msg.(string); ok {
									businessErr = fmt.Errorf("%s", msgStr)
								}
							}
						}
					}
				}
			}
		}

		// 检查是否有错误（包括HTTP错误和业务错误）
		var err error
		if len(c.Errors) > 0 {
			err = c.Errors.Last()
		} else if businessErr != nil {
			err = businessErr
		}

		// 发布操作日志事件
		event.PublishOperLogEvent(
			c.Request.Context(),
			c,
			config.Title,
			config.BusinessType,
			operName,
			deptName,
			result,
			err,
			startTime,
		)
	}
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 写入响应数据
func (w *responseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return w.ResponseWriter.Write(data)
}

// WriteString 写入字符串响应数据
func (w *responseWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// 便捷方法

// OperLogInsert 新增操作日志中间件
func OperLogInsert(title string) gin.HandlerFunc {
	return OperLog(OperLogConfig{
		Title:          title,
		BusinessType:   event.BusinessTypeInsert,
		IsSaveReqData:  true,
		IsSaveRespData: true,
	})
}

// OperLogUpdate 修改操作日志中间件
func OperLogUpdate(title string) gin.HandlerFunc {
	return OperLog(OperLogConfig{
		Title:          title,
		BusinessType:   event.BusinessTypeUpdate,
		IsSaveReqData:  true,
		IsSaveRespData: true,
	})
}

// OperLogDelete 删除操作日志中间件
func OperLogDelete(title string) gin.HandlerFunc {
	return OperLog(OperLogConfig{
		Title:          title,
		BusinessType:   event.BusinessTypeDelete,
		IsSaveReqData:  true,
		IsSaveRespData: true,
	})
}

// OperLogExport 导出操作日志中间件
func OperLogExport(title string) gin.HandlerFunc {
	return OperLog(OperLogConfig{
		Title:          title,
		BusinessType:   event.BusinessTypeExport,
		IsSaveReqData:  true,
		IsSaveRespData: true,
	})
}

// OperLogGrant 授权操作日志中间件
func OperLogGrant(title string) gin.HandlerFunc {
	return OperLog(OperLogConfig{
		Title:          title,
		BusinessType:   event.BusinessTypeGrant,
		IsSaveReqData:  true,
		IsSaveRespData: true,
	})
}

// OperLogQuery 查询操作日志中间件
func OperLogQuery(title string) gin.HandlerFunc {
	return OperLog(OperLogConfig{
		Title:          title,
		BusinessType:   event.BusinessTypeOther,
		IsSaveReqData:  true,
		IsSaveRespData: true,
	})
}
