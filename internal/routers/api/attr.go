package api

import (
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type AttrController struct {
}

func NewAttrController() *AttrController {
	return &AttrController{}
}

// GetList 获取属性定义列表
func (con *AttrController) GetList(c *gin.Context) {
	param := req.AttrListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, attrs, err := svc.GetAttrList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     attrs,
	})
}

// GetDetail 获取属性定义详情
func (con *AttrController) GetDetail(c *gin.Context) {
	id := convert.StrTo(c.Param("id")).MustUInt64()
	if id == 0 {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("无效的ID"))
		return
	}

	svc := service.New(c.Request.Context())
	attr, err := svc.GetAttr(id)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, attr)
}

// 创建属性定义
func (con *AttrController) Create(c *gin.Context) {
	param := req.CreateAttrRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateAttr(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// 更新属性定义
func (con *AttrController) Update(c *gin.Context) {
	param := req.UpdateAttrRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateAttr(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}
