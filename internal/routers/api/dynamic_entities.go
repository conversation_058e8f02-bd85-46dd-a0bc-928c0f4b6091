package api

import (
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type DynamicEntityController struct{}

func NewDynamicEntityController() *DynamicEntityController {
	return &DynamicEntityController{}
}

// GetList 获取动态实体列表
func (con *DynamicEntityController) GetList(c *gin.Context) {
	param := req.DynamicEntityListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, entities, err := svc.GetDynamicEntityList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     entities,
	})
}

// GetDetail 获取动态实体详情
func (con *DynamicEntityController) GetDetail(c *gin.Context) {
	id := convert.StrTo(c.Param("id")).MustUInt64()
	if id == 0 {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("无效的ID"))
		return
	}

	svc := service.New(c.Request.Context())
	entity, err := svc.GetDynamicEntity(id)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, entity)
}

// Create 创建动态实体
func (con *DynamicEntityController) Create(c *gin.Context) {
	param := req.CreateDynamicEntityRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateDynamicEntity(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// Update 更新动态实体
func (con *DynamicEntityController) Update(c *gin.Context) {
	param := req.UpdateDynamicEntityRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateDynamicEntity(param.Id, &param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// Delete 删除动态实体
func (con *DynamicEntityController) Delete(c *gin.Context) {
	id := convert.StrTo(c.Param("id")).MustUInt64()
	if id == 0 {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("无效的ID"))
		return
	}

	svc := service.New(c.Request.Context())
	err := svc.DeleteDynamicEntity(id)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// BatchDelete 批量删除动态实体
func (con *DynamicEntityController) BatchDelete(c *gin.Context) {
	param := req.DeleteDynamicEntityRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.DeleteDynamicEntities(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// GetForm 获取动态实体表单结构
func (con *DynamicEntityController) GetForm(c *gin.Context) {
	param := req.GetDynamicEntityFormRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	result, err := svc.GetDynamicEntityForm(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, result)
}

// Validate 验证动态实体数据
func (con *DynamicEntityController) Validate(c *gin.Context) {
	param := req.ValidateDynamicEntityRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	result, err := svc.ValidateDynamicEntity(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, result)
}
