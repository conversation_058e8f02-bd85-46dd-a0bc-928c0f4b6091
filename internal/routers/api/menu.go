package api

import (
	"medlink/internal/service"
	"medlink/pkg/app/req"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type MenuController struct {
}

func NewMenuController() *MenuController {
	return &MenuController{}
}

func (con *MenuController) GetList(c *gin.Context) {
	param := req.MenuListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	menus, err := svc.GetMenuList(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, menus)
	return
}

func (con *MenuController) GetDetailByMenuId(c *gin.Context) {
	menuId := convert.StrTo(c.Param("menu_id")).MustUInt64()
	if menuId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	menu, err := svc.GetMenuByMenuId(menuId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, menu)
	return
}
func (con *MenuController) GetTreeSelect(c *gin.Context) {
	param := req.MenuListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	menus, err := svc.GetMenuList(&param)
	tree := svc.BuildMenuTree(menus)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, tree)
	return
}

func (con *MenuController) GetRoleMenuTreeSelect(c *gin.Context) {
	roleId := convert.StrTo(c.Param("role_id")).MustUInt64()
	param := req.MenuListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	menus, err := svc.GetMenuList(&param)
	tree := svc.BuildMenuTree(menus)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuListFail, utils.WithMessage(err.Error()))
		return
	}
	checkIds := svc.GetMenuIdsByRoleId(roleId)
	utils.Success(c, map[string]interface{}{
		"menus":       tree,
		"checkedKeys": checkIds,
	})
	return
}

func (con *MenuController) CreateMenu(c *gin.Context) {
	param := req.CreateMenuRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateMenu(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateMenuFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *MenuController) UpdateMenu(c *gin.Context) {
	param := req.UpdateMenuRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateMenu(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateMenuFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *MenuController) DeleteMenu(c *gin.Context) {
	menuId := convert.StrTo(c.Param("menu_id")).MustUInt64()
	if menuId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	err := svc.DeleteMenu(menuId)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteMenuFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
