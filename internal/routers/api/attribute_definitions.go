package api

import (
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type AttributeDefinitionController struct {
}

func NewAttributeDefinitionController() *AttributeDefinitionController {
	return &AttributeDefinitionController{}
}

// GetList 获取属性定义列表
func (con *AttributeDefinitionController) GetList(c *gin.Context) {
	param := req.AttributeDefinitionListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, attrs, err := svc.GetAttributeDefinitionList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     attrs,
	})
}

// GetDetail 获取属性定义详情
func (con *AttributeDefinitionController) GetDetail(c *gin.Context) {
	id := convert.StrTo(c.Param("id")).MustUInt64()
	if id == 0 {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("无效的ID"))
		return
	}

	svc := service.New(c.Request.Context())
	attr, err := svc.GetAttributeDefinition(id)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, attr)
}

// Create 创建属性定义
func (con *AttributeDefinitionController) Create(c *gin.Context) {
	param := req.CreateAttributeDefinitionRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateAttributeDefinition(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// Update 更新属性定义
func (con *AttributeDefinitionController) Update(c *gin.Context) {
	param := req.UpdateAttributeDefinitionRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateAttributeDefinition(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// Delete 删除属性定义
func (con *AttributeDefinitionController) Delete(c *gin.Context) {
	id := convert.StrTo(c.Param("id")).MustUInt64()
	if id == 0 {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("无效的ID"))
		return
	}

	svc := service.New(c.Request.Context())
	err := svc.DeleteAttributeDefinition(id)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// BatchDelete 批量删除属性定义
func (con *AttributeDefinitionController) BatchDelete(c *gin.Context) {
	idsStr := c.PostForm("ids")
	if idsStr == "" {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("请选择要删除的记录"))
		return
	}

	idStrs := strings.Split(idsStr, ",")
	ids := make([]uint64, 0, len(idStrs))
	for _, idStr := range idStrs {
		if id, err := strconv.ParseUint(strings.TrimSpace(idStr), 10, 64); err == nil && id > 0 {
			ids = append(ids, id)
		}
	}

	if len(ids) == 0 {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("无效的ID列表"))
		return
	}

	param := req.DeleteAttributeDefinitionRequest{
		Ids:      ids,
		Operater: c.GetString("username"),
	}

	svc := service.New(c.Request.Context())
	err := svc.DeleteAttributeDefinitions(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// GetByEntity 根据实体类型获取属性定义
func (con *AttributeDefinitionController) GetByEntity(c *gin.Context) {
	param := req.GetAttributeDefinitionsByEntityRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	result, err := svc.GetAttributeDefinitionsByEntity(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, result)
}

// GetEntityTypes 获取所有实体类型
func (con *AttributeDefinitionController) GetEntityTypes(c *gin.Context) {
	svc := service.New(c.Request.Context())
	entityTypes, err := svc.GetEntityTypes()
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, entityTypes)
}

// ValidateConfig 验证字段配置
func (con *AttributeDefinitionController) ValidateConfig(c *gin.Context) {
	param := req.ValidateConfigRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	svc := service.New(c.Request.Context())
	result, err := svc.ValidateFieldConfig(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, result)
}

// CopyDefinitions 复制属性定义
func (con *AttributeDefinitionController) CopyDefinitions(c *gin.Context) {
	param := req.CopyAttributeDefinitionsRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CopyAttributeDefinitions(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// BatchUpdateSortOrder 批量更新排序
func (con *AttributeDefinitionController) BatchUpdateSortOrder(c *gin.Context) {
	param := req.BatchUpdateSortOrderRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.BatchUpdateSortOrder(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// GetStats 获取属性定义统计信息
func (con *AttributeDefinitionController) GetStats(c *gin.Context) {
	svc := service.New(c.Request.Context())
	stats, err := svc.GetAttributeDefinitionStats()
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, stats)
}

// GetFieldTypeSchemas 获取字段类型配置模式
func (con *AttributeDefinitionController) GetFieldTypeSchemas(c *gin.Context) {
	svc := service.New(c.Request.Context())
	schemas := svc.GetFieldTypeSchemas()

	utils.Success(c, schemas)
}
