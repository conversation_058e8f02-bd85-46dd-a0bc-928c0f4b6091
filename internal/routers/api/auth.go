package api

import (
	"encoding/json"
	"medlink/global"
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"medlink/pkg/captcha"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type AuthController struct {
	captcha *captcha.SimpleCaptcha
}

func NewAuthController() *AuthController {
	store := captcha.NewMemoryStore()
	return &AuthController{
		captcha: captcha.NewSimpleCaptcha(store),
	}
}

// 获取验证码
func (con *AuthController) CaptchaImage(c *gin.Context) {
	id, b64s, answer, err := con.captcha.Generate()
	if err != nil {
		utils.Error(c, errcode.ServerError, utils.WithMessage(err.Error()))
		return
	}
	spew.Dump(answer)
	utils.Success(c, resp.CaptchaResponse{
		CaptchaEnabled: true,
		Uuid:           id,
		Img:            b64s,
	})
}

// Login 用户登录
func (con *AuthController) Login(c *gin.Context) {
	param := req.LoginRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	// 验证码校验
	verify := con.captcha.Verify(param.Uuid, param.Code, true)
	if !verify {
		// 记录登录失败日志
		svc.ProcessLoginFail(c, param.UserName, "验证码错误")
		utils.Error(c, errcode.InvalidParams, utils.WithMessage("验证码错误"))
		return
	}
	user, err := svc.CheckUserNamePassword(param.UserName, param.Password)
	if err != nil {
		// 记录登录失败日志
		svc.ProcessLoginFail(c, param.UserName, err.Error())
		utils.Error(c, errcode.UnauthorizedAuthNotExist, utils.WithMessage(err.Error()))
		return
	}
	roles := []string{}
	permissions := []string{}
	if user.UserId == 1 {
		roles = append(roles, "admin")
		permissions = append(permissions, "*:*:*")
	} else {
		roles, permissions, err = svc.GetPermissionsByUserId(user.UserId)
		if err != nil {
			utils.Error(c, errcode.UnauthorizedTokenGenerate, utils.WithMessage(err.Error()))
			return
		}
	}
	token, err := app.GenerateToken(user.UserId, user.UserName)
	if err != nil {
		// 记录登录失败日志
		svc.ProcessLoginFail(c, param.UserName, "Token生成失败")
		utils.Error(c, errcode.UnauthorizedTokenGenerate)
		return
	}

	// 缓存
	expireInt64 := convert.StrTo(global.App.Config.GetString("JWT.Expire")).MustInt64()
	expire := time.Duration(expireInt64) * time.Minute
	global.App.Redis.Set(c, app.UserTokenKey(user.UserId), token, expire)
	rJson, _ := json.Marshal(roles)
	global.App.Redis.Set(c, app.UserRolesKey(user.UserId), rJson, expire)
	pJson, _ := json.Marshal(permissions)
	global.App.Redis.Set(c, app.UserPermissionsKey(user.UserId), pJson, expire)
	// 缓存End

	// 登录后处理失败不影响登录流程，只记录错误
	_ = svc.ProcessLoginSuccess(c, user, token, int(expireInt64))

	utils.Success(c, resp.LoginResponse{
		Token:  token,
		Expire: expireInt64,
	})
}

func (con *AuthController) GetInfo(c *gin.Context) {
	userId := c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	user, err := svc.GetUser(userId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}
	roles := []string{}
	perms := []string{}
	if user.Admin {
		roles = append(roles, "admin")
		perms = append(perms, "*:*:*")
	} else {
		roleJson, err := global.App.Redis.Get(c, app.UserRolesKey(userId))
		if err != nil {
			utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
			return
		}
		if roleJson != "" {
			_ = json.Unmarshal([]byte(roleJson), &roles)
		}
		permissionJson, err := global.App.Redis.Get(c, app.UserPermissionsKey(userId))
		if err != nil {
			utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
			return
		}
		if permissionJson != "" {
			_ = json.Unmarshal([]byte(permissionJson), &perms)
		}
	}
	utils.Success(c, map[string]interface{}{
		"permissions": perms,
		"roles":       roles,
		"user":        user,
	})
}

func (con *AuthController) GetRouters(c *gin.Context) {
	userId := c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	routers, err := svc.GetRegularRouters(userId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetRoutersFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, routers)
}

func (con *AuthController) Logout(c *gin.Context) {
	svc := service.New(c.Request.Context())
	token := c.GetString("token")
	if token != "" {
		auth, _ := app.ParseToken(token)
		if auth != nil {
			global.App.Redis.Set(c, app.UserTokenKey(auth.UserId), "", 1*time.Second)
			global.App.Redis.Set(c, app.UserRolesKey(auth.UserId), "", 1*time.Second)
			global.App.Redis.Set(c, app.UserPermissionsKey(auth.UserId), "", 1*time.Second)

			// 退出后处理失败不影响退出流程，只记录错误
			_ = svc.ProcessLogout(c, auth.UserId)
		}
	}
	utils.Success(c, nil)
}

func (con *AuthController) GetProfileByLoginUser(c *gin.Context) {
	userId := c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	user, err := svc.GetUser(userId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}
	var postName []string
	if len(user.Posts) > 0 {
		for i := range user.Posts {
			postName = append(postName, user.Posts[i].PostName)
		}
	}

	var roleName []string
	for i := range user.Roles {
		roleName = append(roleName, user.Roles[i].RoleName)
	}
	utils.Success(c, map[string]interface{}{
		"data":      user,
		"postGroup": postName,
		"roleGroup": roleName,
	})
}

func (con *AuthController) UpdateProfileByLoginUser(c *gin.Context) {
	param := req.UpdateUserProfileRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.UserId = c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	err := svc.UpdateUserProfile(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *AuthController) UpdatePasswordByLoginUser(c *gin.Context) {
	param := req.ChangeNewPwdRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.UserId = c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	if _, err := svc.CheckUserIdPassword(param.UserId, param.OldPassword); err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	changeParam := req.ResetPwdRequest{
		UserId:   param.UserId,
		Password: param.NewPassword,
		Operater: param.Operater,
	}
	if err := svc.ResetPassword(&changeParam); err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
func (con *AuthController) UploadAvatarByLoginUser(c *gin.Context) {
	param := req.ChangeAvatarRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.UserId = c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	if err := svc.ChangeAvatar(&param); err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
