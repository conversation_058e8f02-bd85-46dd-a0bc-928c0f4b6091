package api

import (
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type UserController struct {
}

func NewUserController() *UserController {
	return &UserController{}
}

func (con *UserController) GetList(c *gin.Context) {
	param := req.UserListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, users, err := svc.GetUserList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     users,
	})
}

func (con *UserController) GetDetailByUserId(c *gin.Context) {
	userId := convert.StrTo(c.Param("user_id")).MustUInt64()
	svc := service.New(c.Request.Context())
	// 岗位
	posts, _ := svc.GetPostAll()

	// 角色
	var roles []*resp.RoleResponse
	if userId == 0 {
		// 新增用户时，不需要标识绑定状态
		roles, _ = svc.GetRoleAll()
		utils.Success(c, map[string]interface{}{
			"posts": posts,
			"roles": roles,
		})
		return
	}

	// 编辑用户时，需要标识用户是否绑定了角色
	roles, _ = svc.GetRoleAllWithUserFlag(userId)
	user, err := svc.GetUser(userId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, map[string]interface{}{
		"data":    user,
		"postIds": user.PostIds,
		"posts":   posts,
		"roles":   roles,
		"roleIds": user.RoleIds,
	})
	return
}

func (con *UserController) CreateUser(c *gin.Context) {
	param := req.CreateUserRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateUser(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *UserController) UpdateUser(c *gin.Context) {
	param := req.UpdateUserRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateUser(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *UserController) DeleteUser(c *gin.Context) {
	uid := convert.StrTo(c.Param("user_id"))
	var uids []uint64
	// 判断是否有逗号分隔
	if strings.Contains(uid.String(), ",") {
		ids := strings.Split(uid.String(), ",")
		for _, v := range ids {
			userId := convert.StrTo(v).MustUInt64()
			if userId > 0 {
				uids = append(uids, userId)
			}
		}
	} else {
		uids = append(uids, uid.MustUInt64())
	}
	if len(uids) <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	operater := c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.DeleteUsers(uids, operater)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *UserController) ResetPassword(c *gin.Context) {
	param := req.ResetPwdRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.ResetPassword(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *UserController) ChangeStatus(c *gin.Context) {
	param := req.ChangeUserStatusRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.ChangeStatus(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *UserController) GetAuthRoleByUserId(c *gin.Context) {
	userId := convert.StrTo(c.Param("user_id")).MustUInt64()
	svc := service.New(c.Request.Context())
	user, err := svc.GetUser(userId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, map[string]interface{}{
		"user":  user,
		"roles": user.Roles,
	})
}

func (con *UserController) SaveAuthRole(c *gin.Context) {
	param := req.AuthRolesRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	err := svc.InsertAuthRoles(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorInsertAuthRolesFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *UserController) GetDeptTree(c *gin.Context) {
	param := req.DeptListRequest{}
	svc := service.New(c.Request.Context())
	depts, err := svc.GetDeptList(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDeptListFail, utils.WithMessage(err.Error()))
		return
	}
	tree := svc.BuildDeptTree(depts)
	utils.Success(c, tree)
}
