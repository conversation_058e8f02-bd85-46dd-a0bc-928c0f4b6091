package api

import (
	"medlink/internal/service"
	"medlink/pkg/app/req"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type DeptController struct {
}

func NewDeptController() *DeptController {
	return &DeptController{}
}

func (con *DeptController) GetList(c *gin.Context) {
	param := req.DeptListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	rows, err := svc.GetDeptList(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDeptListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, rows)
}

func (con *DeptController) GetListExcludeChildren(c *gin.Context) {
	deptId := convert.StrTo(c.Param("dept_id")).MustUInt64()
	if deptId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	param := req.DeptListRequest{}
	rows, err := svc.GetDeptListExcludeChildren(&param, deptId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDeptListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, rows)
}

func (con *DeptController) GetDetailByDeptId(c *gin.Context) {
	deptId := convert.StrTo(c.Param("dept_id")).MustUInt64()
	if deptId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	dept, err := svc.GetDept(deptId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDeptFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, dept)
}

func (con *DeptController) CreateDept(c *gin.Context) {
	param := req.CreateDeptRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateDept(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateDeptFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *DeptController) UpdateDept(c *gin.Context) {
	param := req.UpdateDeptRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateDept(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateDeptFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *DeptController) DeleteDept(c *gin.Context) {
	deptId := convert.StrTo(c.Param("dept_id")).MustUInt64()
	if deptId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	operater := c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.DeleteDept(deptId, operater)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteDeptFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
