package api

import (
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/errcode"
	"medlink/pkg/tools/convert"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type DictController struct {
}

func NewDictController() *DictController {
	return &DictController{}
}

func (con *DictController) GetDataList(c *gin.Context) {
	param := req.DictDataListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, posts, err := svc.GetDictDataList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDictDataListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     posts,
	})
}

func (con *DictController) GetDataDetailByDictCode(c *gin.Context) {
	dictCode := convert.StrTo(c.Param("dict_code")).MustUInt64()
	if dictCode <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	dictData, err := svc.GetDictData(dictCode)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDictDataFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, dictData)
}

func (con *DictController) GetDictDataByType(c *gin.Context) {
	dictType := convert.StrTo(c.Param("dict_type")).String()
	if len(dictType) <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	rows, err := svc.GetDictDataByType(dictType)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDictDataListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, rows)
}

func (con *DictController) CreateDictData(c *gin.Context) {
	param := req.CreateDictDataRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateDictData(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateDictDataFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *DictController) UpdateDictData(c *gin.Context) {
	param := req.UpdateDictDataRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}

	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateDictData(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateDictDataFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *DictController) DeleteDictData(c *gin.Context) {
	code := convert.StrTo(c.Param("dict_code"))
	var codes []uint64
	// 判断是否有逗号分隔
	if strings.Contains(code.String(), ",") {
		ids := strings.Split(code.String(), ",")
		for _, v := range ids {
			userId := convert.StrTo(v).MustUInt64()
			if userId > 0 {
				codes = append(codes, userId)
			}
		}
	} else {
		codes = append(codes, code.MustUInt64())
	}
	if len(codes) <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}

	svc := service.New(c.Request.Context())
	err := svc.DeleteDictDatas(codes)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteDictDataFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *DictController) GetTypeList(c *gin.Context) {
	param := req.DictTypeListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	svc := service.New(c.Request.Context())
	total, dictTypeList, err := svc.GetDictTypeList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDictTypeListFail)
		return
	}
	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     dictTypeList,
	})
}
func (con *DictController) GetTypeDetailByDictId(c *gin.Context) {
	dictId := convert.StrTo(c.Param("dict_id")).MustUInt64()
	if dictId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	dictType, err := svc.GetDictType(dictId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDictTypeFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, dictType)
}
func (con *DictController) CreateDictType(c *gin.Context) {
	param := req.CreateDictTypeRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateDictType(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateDictTypeFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
func (con *DictController) UpdateDictType(c *gin.Context) {
	param := req.UpdateDictTypeRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateDictType(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateDictTypeFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
func (con *DictController) DeleteDictType(c *gin.Context) {
	did := convert.StrTo(c.Param("dict_id"))
	var dids []uint64
	// 判断是否有逗号分隔
	if strings.Contains(did.String(), ",") {
		ids := strings.Split(did.String(), ",")
		for _, v := range ids {
			userId := convert.StrTo(v).MustUInt64()
			if userId > 0 {
				dids = append(dids, userId)
			}
		}
	} else {
		dids = append(dids, did.MustUInt64())
	}
	if len(dids) <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	err := svc.DeleteDictTypes(dids)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteDictTypeFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
func (con *DictController) GetDictTypeOptionSelect(c *gin.Context) {
	param := req.DictTypeListRequest{}
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: 1, PageSize: 1000}
	_, posts, err := svc.GetDictTypeList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetDictTypeListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, posts)
}
