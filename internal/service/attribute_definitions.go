package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"strings"

	"github.com/xeipuuv/gojsonschema"
)

// GetAttributeDefinition 获取单个属性定义
func (svc *Service) GetAttributeDefinition(id uint64) (*app.AttributeDefinitionResponse, error) {
	fields := (model.SysAttributeDefinitions{}).GetFields()
	attr, err := svc.dao.GetAttributeDefinitionById(id, fields)
	if err != nil {
		return nil, err
	}

	return svc.buildAttributeDefinitionResponse(attr)
}

// GetAttributeDefinitionList 获取属性定义列表
func (svc *Service) GetAttributeDefinitionList(param *req.AttributeDefinitionListRequest, pager *app.Pager) (int64, []*app.AttributeDefinitionResponse, error) {
	fields := (model.SysAttributeDefinitions{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
		OrderBy:   "sort_order ASC, id ASC",
	}

	attrs, total, err := svc.dao.GetAttributeDefinitionList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}

	responses := make([]*app.AttributeDefinitionResponse, 0, len(attrs))
	for _, attr := range attrs {
		response, err := svc.buildAttributeDefinitionResponse(attr)
		if err != nil {
			return 0, nil, err
		}
		responses = append(responses, response)
	}

	return total, responses, nil
}

// CreateAttributeDefinition 创建属性定义
func (svc *Service) CreateAttributeDefinition(param *req.CreateAttributeDefinitionRequest) error {
	// 验证配置
	if err := svc.validateFieldConfig(param.FieldType, param.Config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 验证数据类型与字段类型的兼容性
	if err := svc.validateDataTypeCompatibility(param.FieldType, param.DataType); err != nil {
		return err
	}

	_, err := svc.dao.CreateAttributeDefinition(param)
	return err
}

// UpdateAttributeDefinition 更新属性定义
func (svc *Service) UpdateAttributeDefinition(param *req.UpdateAttributeDefinitionRequest) error {
	// 如果更新了配置，验证配置
	if param.Config != "" {
		fieldType := param.FieldType
		if fieldType == "" {
			// 如果没有提供字段类型，从数据库获取
			existing, err := svc.dao.GetAttributeDefinitionById(param.Id, []string{"field_type"})
			if err != nil {
				return err
			}
			fieldType = existing.FieldType
		}

		if err := svc.validateFieldConfig(fieldType, param.Config); err != nil {
			return fmt.Errorf("配置验证失败: %v", err)
		}
	}

	// 验证数据类型与字段类型的兼容性
	if param.FieldType != "" && param.DataType != "" {
		if err := svc.validateDataTypeCompatibility(param.FieldType, param.DataType); err != nil {
			return err
		}
	}

	return svc.dao.UpdateAttributeDefinition(param.Id, param)
}

// DeleteAttributeDefinition 删除属性定义
func (svc *Service) DeleteAttributeDefinition(id uint64) error {
	return svc.dao.DeleteAttributeDefinitionById(id)
}

// DeleteAttributeDefinitions 批量删除属性定义
func (svc *Service) DeleteAttributeDefinitions(param *req.DeleteAttributeDefinitionRequest) error {
	return svc.dao.DeleteAttributeDefinitionByIds(param.Ids)
}

// GetAttributeDefinitionsByEntity 根据实体类型获取属性定义
func (svc *Service) GetAttributeDefinitionsByEntity(param *req.GetAttributeDefinitionsByEntityRequest) (*app.EntityAttributesResponse, error) {
	fields := (model.SysAttributeDefinitions{}).GetFields()
	attrs, err := svc.dao.GetAttributeDefinitionsByEntity(param.EntityType, param.IsRequired, fields)
	if err != nil {
		return nil, err
	}

	responses := make([]*app.AttributeDefinitionResponse, 0, len(attrs))
	for _, attr := range attrs {
		response, err := svc.buildAttributeDefinitionResponse(attr)
		if err != nil {
			return nil, err
		}
		responses = append(responses, response)
	}

	return &app.EntityAttributesResponse{
		EntityType: param.EntityType,
		Attributes: responses,
	}, nil
}

// GetEntityTypes 获取所有实体类型
func (svc *Service) GetEntityTypes() ([]string, error) {
	return svc.dao.GetEntityTypes()
}

// ValidateFieldConfig 验证字段配置
func (svc *Service) ValidateFieldConfig(param *req.ValidateConfigRequest) (*app.ConfigValidationResponse, error) {
	err := svc.validateFieldConfig(param.FieldType, param.Config)
	if err != nil {
		return &app.ConfigValidationResponse{
			Valid:   false,
			Errors:  []string{err.Error()},
			Message: "配置验证失败",
		}, nil
	}

	return &app.ConfigValidationResponse{
		Valid:   true,
		Message: "配置验证通过",
	}, nil
}

// CopyAttributeDefinitions 复制属性定义
func (svc *Service) CopyAttributeDefinitions(param *req.CopyAttributeDefinitionsRequest) error {
	return svc.dao.CopyAttributeDefinitions(param.SourceEntityType, param.TargetEntityType)
}

// BatchUpdateSortOrder 批量更新排序
func (svc *Service) BatchUpdateSortOrder(param *req.BatchUpdateSortOrderRequest) error {
	return svc.dao.BatchUpdateSortOrder(param.Items)
}

// GetAttributeDefinitionStats 获取属性定义统计信息
func (svc *Service) GetAttributeDefinitionStats() (*app.AttributeDefinitionStatsResponse, error) {
	stats, err := svc.dao.GetAttributeDefinitionStats()
	if err != nil {
		return nil, err
	}

	response := &app.AttributeDefinitionStatsResponse{
		TotalCount:      stats["total_count"].(int64),
		EntityTypeStats: stats["entity_type_stats"].(map[string]int64),
		FieldTypeStats:  stats["field_type_stats"].(map[string]int64),
		DataTypeStats:   stats["data_type_stats"].(map[string]int64),
		RequiredStats:   stats["required_stats"].(map[string]int64),
	}

	// 获取最近修改的记录
	param := &req.AttributeDefinitionListRequest{}
	pageOption := &app.PageOption{
		Page:     1,
		PageSize: 5,
		OrderBy:  "id DESC", // 按ID倒序，获取最新的记录
	}

	fields := (model.SysAttributeDefinitions{}).GetFields()
	recentAttrs, _, err := svc.dao.GetAttributeDefinitionList(param, fields, pageOption)
	if err == nil && len(recentAttrs) > 0 {
		recentResponses := make([]*app.AttributeDefinitionResponse, 0, len(recentAttrs))
		for _, attr := range recentAttrs {
			response, err := svc.buildAttributeDefinitionResponse(attr)
			if err == nil {
				recentResponses = append(recentResponses, response)
			}
		}
		response.RecentlyModified = recentResponses
	}

	return response, nil
}

// GetFieldTypeSchemas 获取字段类型配置模式
func (svc *Service) GetFieldTypeSchemas() []*app.FieldTypeConfigSchema {
	return app.GetFieldTypeSchemas()
}

// buildAttributeDefinitionResponse 构建属性定义响应
func (svc *Service) buildAttributeDefinitionResponse(attr *model.SysAttributeDefinitions) (*app.AttributeDefinitionResponse, error) {
	response := &app.AttributeDefinitionResponse{
		Id:         attr.Id,
		EntityType: attr.EntityType,
		AttrCode:   attr.AttrCode,
		AttrName:   attr.AttrName,
		FieldType:  attr.FieldType,
		DataType:   attr.DataType,
		IsRequired: attr.IsRequired,
		SortOrder:  attr.SortOrder,
		ConfigRaw:  attr.Config,
	}

	// 解析配置JSON
	if err := response.SetConfig(attr.Config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %v", err)
	}

	return response, nil
}

// validateFieldConfig 验证字段配置
func (svc *Service) validateFieldConfig(fieldType, config string) error {
	if config == "" {
		return errors.New("配置不能为空")
	}

	// 检查是否为有效的JSON
	var configMap map[string]interface{}
	if err := json.Unmarshal([]byte(config), &configMap); err != nil {
		return fmt.Errorf("配置必须是有效的JSON格式: %v", err)
	}

	// 获取字段类型的配置模式
	schemas := app.GetFieldTypeSchemas()
	var schema *app.FieldTypeConfigSchema
	for _, s := range schemas {
		if s.FieldType == fieldType {
			schema = s
			break
		}
	}

	if schema == nil {
		return fmt.Errorf("不支持的字段类型: %s", fieldType)
	}

	// 验证必需字段
	for _, required := range schema.Required {
		if _, exists := configMap[required]; !exists {
			return fmt.Errorf("缺少必需字段: %s", required)
		}
	}

	// 使用JSON Schema验证配置
	if schema.Schema != nil {
		schemaLoader := gojsonschema.NewGoLoader(schema.Schema)
		documentLoader := gojsonschema.NewGoLoader(configMap)

		result, err := gojsonschema.Validate(schemaLoader, documentLoader)
		if err != nil {
			return fmt.Errorf("配置验证失败: %v", err)
		}

		if !result.Valid() {
			var errors []string
			for _, desc := range result.Errors() {
				errors = append(errors, desc.String())
			}
			return fmt.Errorf("配置验证失败: %s", strings.Join(errors, "; "))
		}
	}

	return nil
}

// validateDataTypeCompatibility 验证数据类型与字段类型的兼容性
func (svc *Service) validateDataTypeCompatibility(fieldType, dataType string) error {
	schemas := app.GetFieldTypeSchemas()
	for _, schema := range schemas {
		if schema.FieldType == fieldType {
			for _, supportedType := range schema.DataTypes {
				if supportedType == dataType {
					return nil
				}
			}
			return fmt.Errorf("字段类型 %s 不支持数据类型 %s，支持的类型: %v",
				fieldType, dataType, schema.DataTypes)
		}
	}

	return fmt.Errorf("不支持的字段类型: %s", fieldType)
}
