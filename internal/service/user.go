package service

import (
	"errors"
	"fmt"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"medlink/pkg/tools"
	"medlink/pkg/tools/convert"
	"strings"
	"time"

	"gorm.io/gorm"
)

func (svc *Service) CheckUserNamePassword(username, password string) (*model.SysUser, error) {
	fields := (model.SysUser{}).GetFields()
	user, err := svc.dao.GetUserByUserName(username, "0", fields)
	if err != nil {
		return nil, err
	}
	if !svc.checkPassword(user, password) {
		return nil, errors.New("密码错误")
	}
	return user, nil
}

func (svc *Service) CheckUserIdPassword(userId uint64, password string) (*model.SysUser, error) {
	fields := (model.SysUser{}).GetFields()
	user, err := svc.dao.GetUserByUserId(userId, "0", fields)
	if err != nil {
		return nil, err
	}
	if !svc.checkPassword(user, password) {
		return nil, errors.New("密码错误")
	}
	return user, nil
}

func (svc *Service) checkPassword(user *model.SysUser, password string) bool {
	if user.UserId == 0 {
		return false
	}
	if password == "" {
		return false
	}
	pwdNew := user.UserName + password + user.Salt
	pwdNew = tools.MustEncryptString(pwdNew)
	return strings.Compare(user.Password, pwdNew) != -1
}

// GetPermissionsByUserId 获取用户权限
func (svc *Service) GetPermissionsByUserId(userId uint64) ([]string, []string, error) {
	roles, err := svc.dao.GetUserRolesByUserIds([]uint64{userId})
	if err != nil {
		return nil, nil, err
	}
	if _, ok := roles[userId]; !ok {
		return nil, nil, errors.New("用户没有角色")
	}
	roleKeys := make([]string, 0, len(roles[userId]))
	roleIds := make([]uint64, 0, len(roles[userId]))
	for _, role := range roles[userId] {
		roleIds = append(roleIds, role.RoleId)
		roleKeys = append(roleKeys, role.RoleKey)
	}
	menus, err := svc.dao.GetRoleMenusByRoleIds(roleIds)
	if err != nil {
		return nil, nil, err
	}
	permissions := make([]string, 0, len(menus))
	for i := range menus {
		if len(menus[i]) > 0 {
			for j := range menus[i] {
				if menus[i][j].Perms == "" {
					continue
				}
				permissions = append(permissions, menus[i][j].Perms)
			}
		}
	}
	return roleKeys, permissions, nil

}

// TODO 构建用户返回值
func (svc *Service) buildUserResponseByUsers(users []*model.SysUser) (map[uint64]*resp.UserResponse, error) {
	userLen := len(users)
	if userLen == 0 {
		return nil, errors.New("用户为空")
	}

	userIds := make([]uint64, 0, userLen)
	deptIds := make([]uint64, 0, userLen)
	for i := range users {
		userIds = append(userIds, users[i].UserId)
		deptIds = append(deptIds, users[i].DeptId)
	}

	// 获取部门信息(同一批租户信息)
	fields := (model.SysDept{}).GetFields()
	deptMap, err := svc.dao.GetDeptMapByDeptIds(deptIds, fields)
	if err != nil {
		return nil, err
	}

	// 岗位详情
	userPosts, err := svc.dao.GetUserPostsByUserIds(userIds)
	if err != nil {
		return nil, err
	}

	// 角色详情
	userRoles, err := svc.dao.GetUserRolesByUserIds(userIds)
	if err != nil {
		return nil, err
	}

	userList := make(map[uint64]*resp.UserResponse)
	for _, user := range users {
		// 部门
		buildDept := new(resp.DeptResponse)
		if _, ok := deptMap[user.DeptId]; ok {
			buildDept = svc.buildDeptResponse(deptMap[user.DeptId])
			if buildDept.ParentId != 0 {
				buildDept.ParentName = deptMap[buildDept.ParentId].DeptName
			}
		}
		// 岗位
		userPostLen := len(userPosts[user.UserId])
		postIds := make([]uint64, 0, userPostLen)
		postList := make([]*resp.PostResponse, 0, userPostLen)
		if userPostLen > 0 {
			for i := range userPosts[user.UserId] {
				postIds = append(postIds, userPosts[user.UserId][i].PostId)
				postList = append(postList, &resp.PostResponse{
					CreateBy:   userPosts[user.UserId][i].CreateBy,
					CreateTime: tools.Time2String(userPosts[user.UserId][i].CreateTime),
					PostCode:   userPosts[user.UserId][i].PostCode,
					PostId:     userPosts[user.UserId][i].PostId,
					PostName:   userPosts[user.UserId][i].PostName,
					PostSort:   userPosts[user.UserId][i].PostSort,
					Remark:     userPosts[user.UserId][i].Remark,
					Status:     userPosts[user.UserId][i].Status,
					UpdateBy:   userPosts[user.UserId][i].UpdateBy,
					UpdateTime: tools.Time2String(userPosts[user.UserId][i].UpdateTime),
				})
			}
		}
		// 角色
		userRoleLen := len(userRoles[user.UserId])
		roleIds := make([]uint64, 0, userRoleLen)
		roleList := make([]*resp.RoleResponse, 0, userRoleLen)
		if userRoleLen > 0 {
			for i := range userRoles[user.UserId] {
				roleIds = append(roleIds, userRoles[user.UserId][i].RoleId)
			}

			//DeptIds
			deptList, _ := svc.dao.GetRoleDeptsByRoleIds(roleIds)
			//MenuIds,Permissions
			menuList, _ := svc.dao.GetRoleMenusByRoleIds(roleIds)
			for i := range userRoles[user.UserId] {
				rid := userRoles[user.UserId][i].RoleId

				var deptIds []uint64
				if _, ok := deptList[rid]; ok {
					for _, d := range deptList[rid] {
						deptIds = append(deptIds, d.DeptId)
					}
				}
				var menuIds []uint64
				var perms []string
				if _, ok := menuList[rid]; ok {
					for _, m := range menuList[rid] {
						menuIds = append(menuIds, m.MenuId)
						if len(m.Perms) > 0 {
							perms = append(perms, m.Perms)
						}
					}
				}
				roleList = append(roleList, &resp.RoleResponse{
					CreateBy:          userRoles[user.UserId][i].CreateBy,
					CreateTime:        tools.Time2String(userRoles[user.UserId][i].CreateTime),
					DataScope:         userRoles[user.UserId][i].DataScope,
					DelFlag:           userRoles[user.UserId][i].DelFlag,
					DeptCheckStrictly: userRoles[user.UserId][i].DeptCheckStrictly == 1,
					DeptIds:           deptIds,
					MenuCheckStrictly: userRoles[user.UserId][i].MenuCheckStrictly == 1,
					MenuIds:           menuIds,
					Permissions:       perms,
					Remark:            userRoles[user.UserId][i].Remark,
					RoleId:            userRoles[user.UserId][i].RoleId,
					RoleKey:           userRoles[user.UserId][i].RoleKey,
					RoleName:          userRoles[user.UserId][i].RoleName,
					RoleSort:          userRoles[user.UserId][i].RoleSort,
					Status:            userRoles[user.UserId][i].Status,
					UpdateBy:          userRoles[user.UserId][i].UpdateBy,
					UpdateTime:        tools.Time2String(userRoles[user.UserId][i].UpdateTime),
					Flag:              true,
				})
			}
		}
		userList[user.UserId] = &resp.UserResponse{
			Admin:       user.UserId == 1,
			Avatar:      user.Avatar,
			UserId:      user.UserId,
			DeptId:      user.DeptId,
			NickName:    user.NickName,
			UserName:    user.UserName,
			Email:       user.Email,
			PhoneNumber: user.Phonenumber,
			Sex:         user.Sex,
			Status:      user.Status,
			DelFlag:     user.DelFlag,
			LoginIp:     user.LoginIp,
			LoginDate:   tools.Time2String(user.LoginDate),
			CreateBy:    user.CreateBy,
			CreateTime:  tools.Time2String(user.CreateTime),
			Remark:      user.Remark,
			Dept:        buildDept,
			PostIds:     postIds,
			Posts:       postList,
			RoleIds:     roleIds,
			Roles:       roleList,
		}

	}
	return userList, nil
}

func (svc *Service) GetUserList(param *req.UserListRequest, pager *app.Pager) (int64, []*resp.UserResponse, error) {
	fields := model.SysUser{}.GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
	}
	rows, total, err := svc.dao.GetUserList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}
	if total == 0 {
		return 0, nil, nil
	}
	buf, err := svc.buildUserResponseByUsers(rows)
	if err != nil {
		return 0, nil, err
	}
	users := make([]*resp.UserResponse, 0, len(rows))
	for _, row := range rows {
		if _, ok := buf[row.UserId]; ok {
			users = append(users, buf[row.UserId])
		}
	}
	return total, users, nil
}

func (svc *Service) GetUser(userId uint64) (*resp.UserResponse, error) {
	fields := model.SysUser{}.GetFields()
	user, err := svc.dao.GetUserByUserId(userId, "", fields)
	if err != nil {
		return nil, err
	}
	buf, err := svc.buildUserResponseByUsers([]*model.SysUser{user})
	if err != nil {
		return nil, err
	}
	if userResp, ok := buf[user.UserId]; ok {
		return userResp, nil
	}
	return nil, errors.New("构建用户信息失败")
}

func (svc *Service) CreateUser(param *req.CreateUserRequest) error {
	if svc.CheckUserNameUnique(param.UserName) {
		return errors.New("新增用户'" + param.UserName + "'失败，登录账号已存在")
	}
	if svc.CheckPhoneUnique(param.PhoneNumber) {
		return errors.New("新增用户'" + param.UserName + "'失败，手机号码已存在")
	}
	if svc.CheckEmailUnique(param.Email) {
		return errors.New("新增用户'" + param.UserName + "'失败，邮箱账号已存在")
	}
	if param.DeptId > 0 {
		existByDept, err := svc.dao.GetDeptById(param.DeptId, []string{"dept_id"})
		if err != nil && err != gorm.ErrRecordNotFound {
			return err
		}
		if existByDept.DeptId == 0 {
			return errors.New("部门不存在")
		}
	}
	var validPostIds []uint64
	if len(param.PostIds) > 0 {
		var postIds []uint64
		pIds := strings.Split(param.PostIds, ",")
		for _, pId := range pIds {
			postIds = append(postIds, convert.StrTo(pId).MustUInt64())
		}
		posts, err := svc.dao.GetPostByPostIds(postIds, []string{"post_id"})
		if err != nil {
			return err
		}
		if len(posts) > 0 {
			for _, p := range posts {
				validPostIds = append(validPostIds, p.PostId)
			}
		}
	}

	var validRoleIds []uint64
	if len(param.RoleIds) > 0 {
		var roleIds []uint64
		rIds := strings.Split(param.RoleIds, ",")
		for _, rId := range rIds {
			roleIds = append(roleIds, convert.StrTo(rId).MustUInt64())
		}
		roles, err := svc.dao.GetRoleByRoleIds(roleIds, []string{"role_id"})
		if err != nil {
			return err
		}
		if len(roles) > 0 {
			for _, r := range roles {
				validRoleIds = append(validRoleIds, r.RoleId)
			}
		}
	}
	if err := svc.dao.CreateUser(param, validPostIds, validRoleIds); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateUser(param *req.UpdateUserRequest) error {
	fields := model.SysUser{}.GetFields()
	existByUserId, err := svc.dao.GetUserByUserId(param.UserId, "", fields)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	var validPostIds []uint64
	if len(param.PostIds) > 0 {
		var postIds []uint64
		pIds := strings.Split(param.PostIds, ",")
		for _, pId := range pIds {
			postIds = append(postIds, convert.StrTo(pId).MustUInt64())
		}
		posts, err := svc.dao.GetPostByPostIds(postIds, []string{"post_id"})
		if err != nil {
			return err
		}
		if len(posts) > 0 {
			for _, p := range posts {
				validPostIds = append(validPostIds, p.PostId)
			}
		}
	}
	var validRoleIds []uint64
	if len(param.RoleIds) > 0 {
		var roleIds []uint64
		rIds := strings.Split(param.RoleIds, ",")
		for _, rId := range rIds {
			roleIds = append(roleIds, convert.StrTo(rId).MustUInt64())
		}
		roles, err := svc.dao.GetRoleByRoleIds(roleIds, []string{"role_id"})
		if err != nil {
			return err
		}
		if len(roles) > 0 {
			for _, r := range roles {
				validRoleIds = append(validRoleIds, r.RoleId)
			}
		}
	}
	values := map[string]interface{}{}
	if param.DeptId != "" {
		deptId := convert.StrTo(param.DeptId).MustUInt64()
		if existByUserId.DeptId != deptId {
			if _, err := svc.dao.GetDeptById(deptId, []string{"dept_id"}); err != nil {
				return err
			}
			values["dept_id"] = deptId
		}
	}
	if param.UserName != "" {
		if param.UserName != existByUserId.UserName && svc.CheckUserNameUnique(param.UserName) {
			return errors.New("修改用户'" + param.UserName + "'失败，登录账号已存在")
		}
		values["user_name"] = param.UserName
	}
	if param.PhoneNumber != "" {
		if param.PhoneNumber != existByUserId.Phonenumber && svc.CheckPhoneUnique(param.PhoneNumber) {
			return errors.New("修改用户'" + param.UserName + "'失败，手机号码已存在")
		}
		values["phonenumber"] = param.PhoneNumber
	}
	if param.Email != "" {
		if param.Email != existByUserId.Email && svc.CheckEmailUnique(param.Email) {
			return errors.New("修改用户'" + param.UserName + "'失败，邮箱账号已存在")
		}
		values["email"] = param.Email
	}
	if param.Avatar != "" && param.Avatar != existByUserId.Avatar {
		values["avatar"] = param.Avatar
	}
	if param.Sex != "" && param.Sex != existByUserId.Sex {
		values["sex"] = param.Sex
	}
	if param.Password != "" {
		pwdNew := existByUserId.UserName + param.Password + existByUserId.Salt
		pwdNew = tools.MustEncryptString(pwdNew)
		if pwdNew != existByUserId.Password {
			values["password"] = pwdNew
			values["pwd_update_date"] = time.Now()
		}
	}
	if param.Status != "" {
		status := "0"
		if param.Status == "1" {
			status = "1"
		}
		values["status"] = status
	}
	if param.Remark != "" && param.Remark != existByUserId.Remark {
		values["remark"] = param.Remark
	}
	values["update_time"] = time.Now()
	values["update_by"] = existByUserId.UserName
	if err = svc.dao.UpdateUser(existByUserId.UserId, values, validPostIds, validRoleIds); err != nil {
		return err
	}
	return nil
}

// DeleteUsers 删除用户
func (svc *Service) DeleteUsers(ids []uint64, operater string) error {
	if len(ids) == 0 {
		return nil
	}
	if len(ids) == 1 {
		return svc.dao.DeleteUserById(ids[0], operater)
	}
	return svc.dao.DeleteUserByIds(ids, operater)
}

func (svc *Service) ResetPassword(param *req.ResetPwdRequest) error {
	existByUserId, err := svc.dao.GetUserByUserId(param.UserId, "", []string{"user_id", "user_name"})
	if err != nil {
		return err
	}
	salt := tools.GenerateSubId(6)
	pwdNew := existByUserId.UserName + param.Password + salt
	pwdNew = tools.MustEncryptString(pwdNew)
	values := map[string]interface{}{
		"salt":        salt,
		"password":    pwdNew,
		"update_by":   param.Operater,
		"update_time": time.Now(),
	}
	if err = svc.dao.UpdateUser(existByUserId.UserId, values, nil, nil); err != nil {
		return err
	}
	return nil
}

func (svc *Service) ChangeStatus(param *req.ChangeUserStatusRequest) error {
	existByUserId, err := svc.dao.GetUserByUserId(param.UserId, "", []string{"user_id"})
	if err != nil {
		return err
	}
	if existByUserId.UserId == 0 {
		return errors.New("用户不存在")
	}
	values := map[string]interface{}{
		"status":      param.Status,
		"update_by":   param.Operater,
		"update_time": time.Now(),
	}
	if err = svc.dao.UpdateUser(existByUserId.UserId, values, nil, nil); err != nil {
		return err
	}
	return nil
}

// ChangeAvatar 更新用户头像
func (svc *Service) ChangeAvatar(param *req.ChangeAvatarRequest) error {
	existByUserId, err := svc.dao.GetUserByUserId(param.UserId, "", []string{"user_id"})
	if err != nil {
		return err
	}
	values := map[string]interface{}{
		"avatar":      param.Avatar,
		"update_by":   param.Operater,
		"update_time": time.Now(),
	}
	if err = svc.dao.UpdateUser(existByUserId.UserId, values, nil, nil); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateUserProfile(param *req.UpdateUserProfileRequest) error {
	existByUserId, err := svc.dao.GetUserByUserId(param.UserId, "", []string{"user_id"})
	if err != nil {
		return err
	}
	if param.PhoneNumber != existByUserId.Phonenumber && svc.CheckPhoneUnique(param.PhoneNumber) {
		return errors.New("修改用户'" + param.NickName + "'失败，手机号码已存在")
	}
	if param.Email != existByUserId.Email && svc.CheckEmailUnique(param.Email) {
		return errors.New("修改用户'" + param.NickName + "'失败，邮箱账号已存在")
	}
	values := map[string]interface{}{}
	if param.NickName != existByUserId.NickName {
		values["nick_name"] = param.NickName
	}
	if param.Email != existByUserId.Email {
		values["email"] = param.Email
	}
	if param.PhoneNumber != existByUserId.Phonenumber {
		values["phonenumber"] = param.PhoneNumber
	}
	sex := fmt.Sprint(param.Sex)
	if sex != existByUserId.Sex {
		values["sex"] = sex
	}
	values["update_time"] = time.Now()
	values["update_by"] = param.Operater
	if err = svc.dao.UpdateUser(existByUserId.UserId, values, nil, nil); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckUserNameUnique(username string) bool {
	row, err := svc.dao.GetUserByUserName(username, "", []string{"user_id"})
	if err != nil {
		return false
	}
	return row.UserId > 0
}

func (svc *Service) CheckPhoneUnique(phoneNumber string) bool {
	row, err := svc.dao.GetUserByPhoneNumber(phoneNumber, "", []string{"user_id"})
	if err != nil {
		return false
	}
	return row.UserId > 0
}

func (svc *Service) CheckEmailUnique(email string) bool {
	row, err := svc.dao.GetUserByEmail(email, "", []string{"user_id"})
	if err != nil {
		return false
	}
	return row.UserId > 0
}

// InsertAuthRoles 插入用户角色
func (svc *Service) InsertAuthRoles(param *req.AuthRolesRequest) error {
	// 根据角色查找关联userid
	roleIds, err := svc.dao.GetRoleIdsByUserId(param.UserId)
	if err != nil {
		return err
	}
	var roleIdList []uint64
	if param.RoleIds != "" {
		buf := strings.Split(param.RoleIds, ",")
		for _, v := range buf {
			roleIdList = append(roleIdList, convert.StrTo(v).MustUInt64())
		}
	}
	exist := make([]uint64, 0, len(roleIds))
	rows := make([]string, 0, len(roleIdList))
	for _, roleId := range roleIdList {
		if len(roleIds) > 0 && tools.InArrayUint64(roleIds, roleId) {
			continue
		}
		if len(exist) > 0 && tools.InArrayUint64(exist, roleId) {
			continue
		} else {
			exist = append(exist, roleId)
		}

		// row 格式: user_id, role_id
		row := fmt.Sprintf("%d,%d", param.UserId, roleId)
		rows = append(rows, row)
	}
	return svc.dao.BatchUserRole(rows)
}
