package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"strings"

	"github.com/davecgh/go-spew/spew"
	"github.com/xeipuuv/gojsonschema"
)

// 构建属性定义响应
func (svc *Service) buildAttrResponse(attr *model.SysAttributeDefinitions) (*resp.AttrResponse, error) {
	response := &resp.AttrResponse{
		Id:         attr.Id,
		EntityType: attr.EntityType,
		AttrCode:   attr.AttrCode,
		AttrName:   attr.AttrName,
		FieldType:  attr.FieldType,
		DataType:   attr.DataType,
		IsRequired: attr.IsRequired,
		SortOrder:  attr.SortOrder,
		ConfigRaw:  attr.Config,
	}

	// 解析配置JSON
	if err := response.SetConfig(attr.Config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %v", err)
	}

	return response, nil
}

// 获取单个属性定义
func (svc *Service) GetAttr(id uint64) (*resp.AttrResponse, error) {
	fields := (model.SysAttributeDefinitions{}).GetFields()
	attr, err := svc.dao.GetAttrById(id, fields)
	if err != nil {
		return nil, err
	}

	return svc.buildAttrResponse(attr)
}

// 获取属性定义列表
func (svc *Service) GetAttrList(param *req.AttrListRequest, pager *app.Pager) (int64, []*resp.AttrResponse, error) {
	fields := (model.SysAttributeDefinitions{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
		OrderBy:   "sort_order ASC, id ASC",
	}

	attrs, total, err := svc.dao.GetAttrList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}

	responses := make([]*resp.AttrResponse, 0, len(attrs))
	for _, attr := range attrs {
		response, err := svc.buildAttrResponse(attr)
		if err != nil {
			return 0, nil, err
		}
		responses = append(responses, response)
	}

	return total, responses, nil
}

// 创建属性定义
func (svc *Service) CreateAttr(param *req.CreateAttrRequest) error {
	// 检查属性代码是否已存在
	existing, _ := svc.dao.GetAttrByCode(param.EntityType, param.AttrCode, []string{"id"})
	if existing != nil {
		return errors.New("属性代码已存在")
	}
	// 验证配置
	if err := svc.validateFieldConfig(param.FieldType, param.Config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 验证数据类型与字段类型的兼容性
	if err := svc.validateDataTypeCompatibility(param.FieldType, param.DataType); err != nil {
		return err
	}
	_, err := svc.dao.CreateAttr(param)
	return err
}

// 更新属性定义
func (svc *Service) UpdateAttr(param *req.UpdateAttrRequest) error {
	// 检查记录是否存在
	existing, err := svc.dao.GetAttrById(param.Id, []string{"id", "entity_type", "attr_code", "field_type"})
	if err != nil {
		return err
	}

	// 如果更新了配置，验证配置
	if param.Config != "" {
		fieldType := param.FieldType
		if fieldType == "" {
			// 如果没有提供字段类型，从数据库获取
			fieldType = existing.FieldType
		}

		if err := svc.validateFieldConfig(fieldType, param.Config); err != nil {
			return fmt.Errorf("配置验证失败: %v", err)
		}
	}

	// 验证数据类型与字段类型的兼容性
	if param.FieldType != "" && param.DataType != "" {
		if err := svc.validateDataTypeCompatibility(param.FieldType, param.DataType); err != nil {
			return err
		}
	}

	// 如果更新了属性代码，检查新代码是否已存在
	if param.AttrCode != "" && param.AttrCode != existing.AttrCode {
		entityType := param.EntityType
		if entityType == "" {
			entityType = existing.EntityType
		}

		duplicate, _ := svc.dao.GetAttrByCode(entityType, param.AttrCode, []string{"id"})
		if duplicate != nil && duplicate.Id != param.Id {
			return errors.New("属性代码已存在")
		}
	}
	return svc.dao.UpdateAttr(param.Id, param)
}

// ValidateFieldConfig 验证字段配置
func (svc *Service) ValidateFieldConfig(param *req.ValidateConfigRequest) (*resp.ConfigValidationResponse, error) {
	err := svc.validateFieldConfig(param.FieldType, param.Config)
	if err != nil {
		return &resp.ConfigValidationResponse{
			Valid:   false,
			Errors:  []string{err.Error()},
			Message: "配置验证失败",
		}, nil
	}

	return &resp.ConfigValidationResponse{
		Valid:   true,
		Message: "配置验证通过",
	}, nil
}

// validateFieldConfig 验证字段配置
func (svc *Service) validateFieldConfig(fieldType, config string) error {
	if config == "" {
		return errors.New("配置不能为空")
	}

	// 检查是否为有效的JSON
	var configMap map[string]interface{}
	if err := json.Unmarshal([]byte(config), &configMap); err != nil {
		return fmt.Errorf("配置必须是有效的JSON格式: %v", err)
	}

	// 获取字段类型的配置模式
	schemas := resp.GetFieldTypeSchemas()
	var schema *resp.FieldTypeConfigSchema
	for _, s := range schemas {
		if s.FieldType == fieldType {
			schema = s
			break
		}
	}

	if schema == nil {
		return fmt.Errorf("不支持的字段类型: %s", fieldType)
	}

	// 验证必需字段
	for _, required := range schema.Required {
		if _, exists := configMap[required]; !exists {
			return fmt.Errorf("缺少必需字段: %s", required)
		}
	}

	// 使用JSON Schema验证配置
	if schema.Schema != nil {
		schemaLoader := gojsonschema.NewGoLoader(schema.Schema)
		documentLoader := gojsonschema.NewGoLoader(configMap)
		spew.Dump(schemaLoader)
		spew.Dump(documentLoader)

		result, err := gojsonschema.Validate(schemaLoader, documentLoader)
		if err != nil {
			return fmt.Errorf("配置验证失败: %v", err)
		}
		if !result.Valid() {
			var errors []string
			for _, desc := range result.Errors() {
				errors = append(errors, desc.String())
			}
			return fmt.Errorf("配置验证失败: %s", strings.Join(errors, "; "))
		}
	}

	return nil
}

// 验证数据类型与字段类型的兼容性
func (svc *Service) validateDataTypeCompatibility(fieldType, dataType string) error {
	schemas := resp.GetFieldTypeSchemas()
	for _, schema := range schemas {
		if schema.FieldType == fieldType {
			for _, supportedType := range schema.DataTypes {
				if supportedType == dataType {
					return nil
				}
			}
			return fmt.Errorf("字段类型 %s 不支持数据类型 %s，支持的类型: %v",
				fieldType, dataType, schema.DataTypes)
		}
	}

	return fmt.Errorf("不支持的字段类型: %s", fieldType)
}
