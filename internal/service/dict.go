package service

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"medlink/pkg/tools"
	"medlink/pkg/tools/convert"
	"time"
)

func (svc *Service) GetDictTypeList(param *req.DictTypeListRequest, pager *app.Pager) (int64, []*resp.DictTypeResponse, error) {
	fields := (model.SysDictType{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
	}
	rows, total, err := svc.dao.GetDictTypeList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.DictTypeResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.DictTypeResponse{
			DictId:     row.DictId,
			DictName:   row.DictName,
			DictType:   row.DictType,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return total, res, nil
}

func (svc *Service) GetDictType(dictId uint64) (*resp.DictTypeResponse, error) {
	fields := (model.SysDictType{}).GetFields()
	row, err := svc.dao.GetDictTypeById(dictId, fields)
	if err != nil {
		return nil, err
	}
	return &resp.DictTypeResponse{
		DictId:     row.DictId,
		DictName:   row.DictName,
		DictType:   row.DictType,
		Status:     row.Status,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
		Remark:     row.Remark,
	}, nil
}

func (svc *Service) CreateDictType(param *req.CreateDictTypeRequest) error {
	if svc.CheckDictNameUniqueForDictType(param.DictName) {
		return errors.New("新增字典类型'" + param.DictName + "'失败，字典名称已存在")
	} else if svc.CheckDictTypeUniqueForDictType(param.DictType) {
		return errors.New("新增字典类型'" + param.DictName + "'失败，字典类型已存在")
	}
	if err := svc.dao.CreateDictType(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateDictType(param *req.UpdateDictTypeRequest) error {
	fields := (model.SysDictType{}).GetFields()
	existByDictType, err := svc.dao.GetDictTypeById(param.DictId, fields)
	if err != nil {
		return err
	}
	if existByDictType.DictId == 0 {
		return errors.New("字典类型不存在")
	}
	values := map[string]interface{}{}
	if param.DictName != existByDictType.DictName {
		if svc.CheckDictNameUniqueForDictType(param.DictName) {
			return errors.New("修改字典类型'" + param.DictName + "'失败，字典名称已存在")
		}
		values["dict_name"] = param.DictName
	}
	if param.DictType != existByDictType.DictType {
		if svc.CheckDictTypeUniqueForDictType(param.DictType) {
			return errors.New("修改字典类型'" + param.DictType + "'失败，字典类型已存在")
		}
		values["dict_type"] = param.DictType
	}
	if existByDictType.Status != param.Status {
		values["status"] = param.Status
	}
	if param.Remark != existByDictType.Remark {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateDictType(existByDictType.DictId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckDictNameUniqueForDictType(dictName string) bool {
	row, err := svc.dao.GetDictTypeByDictName(dictName, []string{"dict_id"})
	if err != nil {
		return false
	}
	return row.DictId > 0
}

func (svc *Service) CheckDictTypeUniqueForDictType(dictType string) bool {
	row, err := svc.dao.GetDictTypeByDictType(dictType, []string{"dict_id"})
	if err != nil {
		return false
	}
	return row.DictId > 0
}

func (svc *Service) DeleteDictTypes(ids []uint64) error {
	return svc.dao.DeleteDictTypes(ids)
}

// ===========================================================

func (svc *Service) GetDictDataList(param *req.DictDataListRequest, pager *app.Pager) (int64, []*resp.DictDataResponse, error) {
	fields := (model.SysDictData{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
	}
	rows, total, err := svc.dao.GetDictDataList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.DictDataResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.DictDataResponse{
			DictCode:   row.DictCode,
			DictSort:   row.DictSort,
			DictLabel:  row.DictLabel,
			DictValue:  row.DictValue,
			DictType:   row.DictType,
			CssClass:   row.CssClass,
			ListClass:  row.ListClass,
			IsDefault:  row.IsDefault,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return total, res, nil
}

func (svc *Service) GetDictData(dictCode uint64) (*resp.DictDataResponse, error) {
	fields := (model.SysDictData{}).GetFields()
	row, err := svc.dao.GetDictDataById(dictCode, fields)
	if err != nil {
		return nil, err
	}
	return &resp.DictDataResponse{
		DictCode:   row.DictCode,
		DictSort:   row.DictSort,
		DictLabel:  row.DictLabel,
		DictValue:  row.DictValue,
		DictType:   row.DictType,
		CssClass:   row.CssClass,
		ListClass:  row.ListClass,
		IsDefault:  row.IsDefault,
		Status:     row.Status,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
		Remark:     row.Remark,
	}, nil
}

func (svc *Service) CreateDictData(param *req.CreateDictDataRequest) error {
	if svc.CheckDictLabelUniqueForDictData(param.DictType, param.DictLabel) {
		return errors.New("新增字典数据'" + param.DictLabel + "'失败，字典数据名称已存在")
	} else if svc.CheckDictValueUniqueForDictData(param.DictType, param.DictValue) {
		return errors.New("新增字典数据'" + param.DictLabel + "'失败，字典数据键值已存在")
	}
	if err := svc.dao.CreateDictData(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateDictData(param *req.UpdateDictDataRequest) error {
	fields := (model.SysDictData{}).GetFields()
	existByDictData, err := svc.dao.GetDictDataById(param.DictCode, fields)
	if err != nil {
		return err
	}
	values := map[string]interface{}{}
	if param.DictLabel != "" && param.DictLabel != existByDictData.DictLabel {
		if svc.CheckDictLabelUniqueForDictData(existByDictData.DictType, param.DictLabel) {
			return errors.New("修改字典数据'" + param.DictLabel + "'失败，字典数据名称已存在")
		}
		values["dict_label"] = param.DictLabel
	}
	if param.DictValue != "" && param.DictValue != existByDictData.DictValue {
		if svc.CheckDictValueUniqueForDictData(existByDictData.DictType, param.DictValue) {
			return errors.New("修改字典数据'" + param.DictValue + "'失败，字典数据键值已存在")
		}
		values["dict_value"] = param.DictValue
	}
	if param.DictSort != "" {
		dictSort := convert.StrTo(param.DictSort).MustInt()
		if dictSort != existByDictData.DictSort {
			values["dict_sort"] = dictSort
		}
	}
	if param.CssClass != "" && param.CssClass != existByDictData.CssClass {
		values["css_class"] = param.CssClass
	}
	if param.ListClass != "" && param.ListClass != existByDictData.ListClass {
		values["list_class"] = param.ListClass
	}
	if param.Status != "" {
		status := "0"
		if param.Status == "1" {
			status = "1"
		}
		values["status"] = status
	}
	if param.Remark != "" && param.Remark != existByDictData.Remark {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateDictData(existByDictData.DictCode, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckDictLabelUniqueForDictData(dictType, dictLabel string) bool {
	row, err := svc.dao.GetDictDataByDictLabel(dictType, dictLabel, []string{"dict_code"})
	if err != nil {
		return false
	}
	return row.DictCode > 0
}

func (svc *Service) CheckDictValueUniqueForDictData(dictType, dictValue string) bool {
	row, err := svc.dao.GetDictDataByDictValue(dictType, dictValue, []string{"dict_code"})
	if err != nil {
		return false
	}
	return row.DictCode > 0
}

func (svc *Service) DeleteDictDatas(ids []uint64) error {
	return svc.dao.DeleteDictDatas(ids)
}

func (svc *Service) GetDictDataByType(dictType string) ([]*resp.DictDataResponse, error) {
	fields := (model.SysDictData{}).GetFields()
	rows, err := svc.dao.GetDictDataByType(dictType, fields)
	if err != nil {
		return nil, err
	}

	res := make([]*resp.DictDataResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.DictDataResponse{
			DictCode:   row.DictCode,
			DictSort:   row.DictSort,
			DictLabel:  row.DictLabel,
			DictValue:  row.DictValue,
			DictType:   row.DictType,
			CssClass:   row.CssClass,
			ListClass:  row.ListClass,
			IsDefault:  row.IsDefault,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return res, nil
}
