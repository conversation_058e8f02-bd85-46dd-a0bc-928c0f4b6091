package service

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"medlink/pkg/tools"
	"medlink/pkg/tools/convert"
	"time"
)

// GetPostAll 获取所有岗位
func (svc *Service) GetPostAll() ([]*resp.PostResponse, error) {
	fields := (model.SysPost{}).GetFields()
	rows, err := svc.dao.GetPostAll(fields)
	if err != nil {
		return nil, err
	}
	res := make([]*resp.PostResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.PostResponse{
			PostId:     row.PostId,
			PostCode:   row.PostCode,
			PostName:   row.PostName,
			PostSort:   row.PostSort,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return res, nil
}

func (svc *Service) GetPostList(param *req.PostListRequest, pager *app.Pager) (int64, []*resp.PostResponse, error) {
	fields := (model.SysPost{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
	}
	rows, total, err := svc.dao.GetPostList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.PostResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.PostResponse{
			PostId:     row.PostId,
			PostCode:   row.PostCode,
			PostName:   row.PostName,
			PostSort:   row.PostSort,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return total, res, nil
}

func (svc *Service) GetPost(postId uint64) (*resp.PostResponse, error) {
	fields := (model.SysPost{}).GetFields()
	row, err := svc.dao.GetPostById(postId, fields)
	if err != nil {
		return nil, err
	}
	return &resp.PostResponse{
		PostId:     row.PostId,
		PostCode:   row.PostCode,
		PostName:   row.PostName,
		PostSort:   row.PostSort,
		Status:     row.Status,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
		Remark:     row.Remark,
	}, nil
}

func (svc *Service) CreatePost(param *req.CreatePostRequest) error {
	if svc.CheckPostCodeUniqueForPost(param.PostCode) {
		return errors.New("新增岗位'" + param.PostName + "'失败，岗位编码已存在")
	} else if svc.CheckPostNameUniqueForPost(param.PostName) {
		return errors.New("新增岗位'" + param.PostName + "'失败，岗位名称已存在")
	}
	if err := svc.dao.CreatePost(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdatePost(param *req.UpdatePostRequest) error {
	fields := (model.SysPost{}).GetFields()
	existByPost, err := svc.dao.GetPostById(param.PostId, fields)
	if err != nil {
		return err
	}
	values := map[string]interface{}{}
	if param.PostCode != "" && param.PostCode != existByPost.PostCode {
		if svc.CheckPostCodeUniqueForPost(param.PostCode) {
			return errors.New("修改岗位'" + param.PostName + "'失败，岗位编码已存在")
		}
		values["post_code"] = param.PostCode
	}
	if param.PostName != "" && param.PostName != existByPost.PostName {
		if svc.CheckPostNameUniqueForPost(param.PostName) {
			return errors.New("修改岗位'" + param.PostName + "'失败，岗位名称已存在")
		}
		values["post_name"] = param.PostName
	}
	if param.PostSort != "" {
		postSort := convert.StrTo(param.PostSort).MustInt()
		if postSort != existByPost.PostSort {
			values["post_sort"] = param.PostSort
		}
	}
	if param.Status != "" {
		status := "0"
		if param.Status == "1" {
			status = "1"
		}
		values["status"] = status
	}
	if param.Remark != "" && param.Remark != existByPost.Remark {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdatePost(existByPost.PostId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckPostCodeUniqueForPost(postCode string) bool {
	row, err := svc.dao.GetPostByPostCode(postCode, []string{"post_id"})
	if err != nil {
		return false
	}
	return row.PostId > 0
}

func (svc *Service) CheckPostNameUniqueForPost(postName string) bool {
	row, err := svc.dao.GetPostByPostName(postName, []string{"post_id"})
	if err != nil {
		return false
	}
	return row.PostId > 0
}

func (svc *Service) DeletePosts(ids []uint64) error {
	return svc.dao.DeletePosts(ids)
}
