package service

import (
	"encoding/json"
	"fmt"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/tools"
	"time"

	"github.com/gin-gonic/gin"
)

// 记录登录日志
func (svc *Service) RecordLoginLog(info *req.LoginLogInfo) error {
	loginInfo := &model.SysLogininfor{
		LoginName:     info.LoginName,
		Ipaddr:        info.ClientInfo.IP,
		LoginLocation: info.ClientInfo.Location,
		Browser:       info.ClientInfo.Browser,
		Os:            info.ClientInfo.OS,
		Status:        info.Status,
		Msg:           info.Message,
		LoginTime:     time.Now(),
	}
	return svc.dao.CreateLoginInfo(loginInfo)
}

// 创建在线用户记录
func (svc *Service) CreateUserOnlineRecord(sessionId, loginName, deptName string, clientInfo *tools.ClientInfo, expireMinutes int) error {
	now := time.Now()
	userOnline := &model.SysUserOnline{
		SessionId:      sessionId,
		LoginName:      loginName,
		DeptName:       deptName,
		Ipaddr:         clientInfo.IP,
		LoginLocation:  clientInfo.Location,
		Browser:        clientInfo.Browser,
		Os:             clientInfo.OS,
		Status:         "on_line",
		StartTimestamp: now,
		LastAccessTime: now,
		ExpireTime:     expireMinutes,
	}
	return svc.dao.CreateUserOnline(userOnline)
}

// // 更新用户最后访问时间
// func (svc *Service) UpdateUserOnlineLastAccess(sessionId string) error {
// 	values := map[string]interface{}{
// 		"last_access_time": time.Now(),
// 	}
// 	return svc.dao.UpdateUserOnline(sessionId, values)
// }

// 移除在线用户记录
func (svc *Service) RemoveUserOnline(sessionId string) error {
	return svc.dao.DeleteUserOnline(sessionId)
}

// 生成会话ID
func (svc *Service) GenerateSessionId(userId uint64) string {
	// 使用用户ID + 时间戳 + 随机数生成会话ID
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("session_%d_%d", userId, timestamp)
}

// 处理登录成功后的逻辑
func (svc *Service) ProcessLoginSuccess(c *gin.Context, user *model.SysUser, token string, expireMinutes int) error {
	clientInfo := tools.GetClientInfo(c)

	// 1. 记录登录日志
	loginLogInfo := &req.LoginLogInfo{
		LoginName:  user.UserName,
		Status:     "0", // 成功
		Message:    "登录成功",
		ClientInfo: clientInfo,
	}
	if err := svc.RecordLoginLog(loginLogInfo); err != nil {
		// 登录日志记录失败不影响登录流程，只记录错误
		// 这里可以添加日志记录
	}

	// 2. 生成会话ID并创建在线用户记录
	sessionId := svc.GenerateSessionId(user.UserId)

	// 获取用户部门名称
	deptName := ""
	if user.DeptId > 0 {
		if dept, err := svc.dao.GetDeptById(user.DeptId, []string{"dept_name"}); err == nil {
			deptName = dept.DeptName
		}
	}

	// 在线用户记录创建失败不影响登录流程
	_ = svc.CreateUserOnlineRecord(sessionId, user.UserName, deptName, clientInfo, expireMinutes)

	// 3. 将会话ID存储到Redis中，与token关联
	sessionKey := app.UserSessionKey(user.UserId)
	expire := time.Duration(expireMinutes) * time.Minute

	// 会话存储失败不影响登录流程
	_ = svc.SetUserSession(c, sessionKey, sessionId, expire)
	return nil
}

// 处理登录失败后的逻辑
func (svc *Service) ProcessLoginFail(c *gin.Context, loginName, message string) error {
	clientInfo := tools.GetClientInfo(c)
	// 记录登录失败日志
	loginLogInfo := &req.LoginLogInfo{
		LoginName:  loginName,
		Status:     "1", // 失败
		Message:    message,
		ClientInfo: clientInfo,
	}
	return svc.RecordLoginLog(loginLogInfo)
}

// 处理退出登录后的逻辑
func (svc *Service) ProcessLogout(c *gin.Context, userId uint64) error {
	// 获取会话ID
	sessionKey := app.UserSessionKey(userId)
	sessionId, err := svc.GetUserSession(c, sessionKey)
	if err == nil && sessionId != "" {
		// 移除在线用户记录,移除失败不影响退出流程
		_ = svc.RemoveUserOnline(sessionId)

		// 清除会话缓存,清除失败不影响退出流程
		_ = svc.DeleteUserSession(c, sessionKey)
	}
	return nil
}

// 设置用户会话
func (svc *Service) SetUserSession(c *gin.Context, key, sessionId string, expire time.Duration) error {
	return svc.redis.Set(c, key, sessionId, expire)
}

// 获取用户会话
func (svc *Service) GetUserSession(c *gin.Context, key string) (string, error) {
	return svc.redis.Get(c, key)
}

// 删除用户会话
func (svc *Service) DeleteUserSession(c *gin.Context, key string) error {
	return svc.redis.Set(c, key, "", 1*time.Second)
}

// 记录操作日志
func (svc *Service) RecordOperLog(info *req.OperLogInfo) error {
	// 序列化请求参数
	operParamJson := ""
	if info.OperParam != nil {
		if paramBytes, err := json.Marshal(info.OperParam); err == nil {
			operParamJson = string(paramBytes)
		}
	}

	// 序列化返回参数
	jsonResultJson := ""
	if info.JsonResult != nil {
		if resultBytes, err := json.Marshal(info.JsonResult); err == nil {
			jsonResultJson = string(resultBytes)
		}
	}

	operLog := &model.SysOperLog{
		Title:         info.Title,
		BusinessType:  info.BusinessType,
		Method:        info.Method,
		RequestMethod: info.RequestMethod,
		OperatorType:  info.OperatorType,
		OperName:      info.OperName,
		DeptName:      info.DeptName,
		OperUrl:       info.OperUrl,
		OperIp:        info.ClientInfo.IP,
		OperLocation:  info.ClientInfo.Location,
		OperParam:     operParamJson,
		JsonResult:    jsonResultJson,
		Status:        info.Status,
		ErrorMsg:      info.ErrorMsg,
		OperTime:      time.Now(),
		CostTime:      uint64(info.CostTime),
	}

	return svc.dao.CreateOperLog(operLog)
}
