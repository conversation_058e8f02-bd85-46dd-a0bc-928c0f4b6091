package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"medlink/internal/model"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"strconv"
	"time"
)

// GetDynamicEntity 获取单个动态实体
func (svc *Service) GetDynamicEntity(id uint64) (*resp.DynamicEntityResponse, error) {
	fields := (model.SysDynamicEntities{}).GetFields()
	entity, err := svc.dao.GetDynamicEntityById(id, fields)
	if err != nil {
		return nil, err
	}

	return svc.buildDynamicEntityResponse(entity)
}

// GetDynamicEntityList 获取动态实体列表
func (svc *Service) GetDynamicEntityList(param *req.DynamicEntityListRequest, pager *app.Pager) (int64, []*resp.DynamicEntityResponse, error) {
	fields := (model.SysDynamicEntities{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
		OrderBy:   "id DESC",
	}

	entities, total, err := svc.dao.GetDynamicEntityList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}

	responses := make([]*resp.DynamicEntityResponse, 0, len(entities))
	for _, entity := range entities {
		response, err := svc.buildDynamicEntityResponse(entity)
		if err != nil {
			return 0, nil, err
		}
		responses = append(responses, response)
	}

	return total, responses, nil
}

// CreateDynamicEntity 创建动态实体
func (svc *Service) CreateDynamicEntity(param *req.CreateDynamicEntityRequest) error {
	// 验证实体类型是否存在属性定义
	attrs, err := svc.dao.GetAttributeDefinitionsByEntity(param.EntityType, nil, []string{"id"})
	if err != nil {
		return err
	}
	if len(attrs) == 0 {
		return errors.New("该实体类型没有定义任何属性")
	}

	// 验证动态属性数据
	err = svc.validateDynamicEntityAttributes(param.EntityType, param.Attributes)
	if err != nil {
		return err
	}

	// 设置默认状态
	if param.Status == "" {
		param.Status = "0"
	}

	_, err = svc.dao.CreateDynamicEntity(param)
	return err
}

// UpdateDynamicEntity 更新动态实体
func (svc *Service) UpdateDynamicEntity(id uint64, param *req.UpdateDynamicEntityRequest) error {
	// 验证动态属性数据（如果提供了属性数据）
	if param.Attributes != nil {
		entityType := param.EntityType
		if entityType == "" {
			// 如果没有提供实体类型，从现有记录中获取
			existing, err := svc.dao.GetDynamicEntityById(id, []string{"entity_type"})
			if err != nil {
				return err
			}
			entityType = existing.EntityType
		}

		err := svc.validateDynamicEntityAttributes(entityType, param.Attributes)
		if err != nil {
			return err
		}
	}

	return svc.dao.UpdateDynamicEntity(id, param)
}

// DeleteDynamicEntity 删除动态实体
func (svc *Service) DeleteDynamicEntity(id uint64) error {
	return svc.dao.DeleteDynamicEntity(id)
}

// DeleteDynamicEntities 批量删除动态实体
func (svc *Service) DeleteDynamicEntities(param *req.DeleteDynamicEntityRequest) error {
	return svc.dao.DeleteDynamicEntities(param.Ids)
}

// GetDynamicEntityForm 获取动态实体表单结构
func (svc *Service) GetDynamicEntityForm(param *req.GetDynamicEntityFormRequest) (*resp.DynamicEntityFormResponse, error) {
	// 获取实体类型的属性定义
	fields := (model.SysAttributeDefinitions{}).GetFields()
	attrs, err := svc.dao.GetAttributeDefinitionsByEntity(param.EntityType, nil, fields)
	if err != nil {
		return nil, err
	}

	if len(attrs) == 0 {
		return nil, errors.New("该实体类型没有定义任何属性")
	}

	// 构建表单结构
	formSchema := make([]*app.AttributeDefinitionResponse, 0, len(attrs))
	for _, attr := range attrs {
		response, err := svc.buildAttributeDefinitionResponse(attr)
		if err != nil {
			return nil, err
		}
		formSchema = append(formSchema, response)
	}

	return &resp.DynamicEntityFormResponse{
		EntityType: param.EntityType,
		FormSchema: formSchema,
	}, nil
}

// ValidateDynamicEntity 验证动态实体数据
func (svc *Service) ValidateDynamicEntity(param *req.ValidateDynamicEntityRequest) (*resp.DynamicEntityValidationResponse, error) {
	err := svc.validateDynamicEntityAttributes(param.EntityType, param.Attributes)
	if err != nil {
		return &resp.DynamicEntityValidationResponse{
			Valid:   false,
			Errors:  []string{err.Error()},
			Message: "数据验证失败",
		}, nil
	}

	return &resp.DynamicEntityValidationResponse{
		Valid:   true,
		Message: "数据验证通过",
	}, nil
}

// validateDynamicEntityAttributes 验证动态实体属性数据
func (svc *Service) validateDynamicEntityAttributes(entityType string, attributes map[string]interface{}) error {
	// 获取属性定义
	fields := (model.SysAttributeDefinitions{}).GetFields()
	attrs, err := svc.dao.GetAttributeDefinitionsByEntity(entityType, nil, fields)
	if err != nil {
		return err
	}

	// 创建属性定义映射
	attrMap := make(map[string]*model.SysAttributeDefinitions)
	for _, attr := range attrs {
		attrMap[attr.AttrCode] = attr
	}

	// 验证必填字段
	for _, attr := range attrs {
		if attr.IsRequired == 1 {
			value, exists := attributes[attr.AttrCode]
			if !exists || value == nil || value == "" {
				return fmt.Errorf("字段 %s (%s) 是必填的", attr.AttrName, attr.AttrCode)
			}
		}
	}

	// 验证字段类型和格式
	for attrCode, value := range attributes {
		attr, exists := attrMap[attrCode]
		if !exists {
			return fmt.Errorf("未知的属性字段: %s", attrCode)
		}

		if value == nil {
			continue // 空值跳过类型验证
		}

		err := svc.validateAttributeValue(attr, value)
		if err != nil {
			return fmt.Errorf("字段 %s (%s) %s", attr.AttrName, attr.AttrCode, err.Error())
		}
	}

	return nil
}

// validateAttributeValue 验证属性值
func (svc *Service) validateAttributeValue(attr *model.SysAttributeDefinitions, value interface{}) error {
	switch attr.DataType {
	case "string":
		if _, ok := value.(string); !ok {
			return errors.New("必须是字符串类型")
		}
	case "integer":
		switch v := value.(type) {
		case int, int32, int64:
			// 已经是整数类型
		case float64:
			// JSON解析的数字默认是float64，检查是否为整数
			if v != float64(int64(v)) {
				return errors.New("必须是整数类型")
			}
		case string:
			// 尝试转换字符串为整数
			if _, err := strconv.ParseInt(v, 10, 64); err != nil {
				return errors.New("必须是整数类型")
			}
		default:
			return errors.New("必须是整数类型")
		}
	case "decimal":
		switch v := value.(type) {
		case int, int32, int64, float32, float64:
			// 数字类型都可以
		case string:
			// 尝试转换字符串为浮点数
			if _, err := strconv.ParseFloat(v, 64); err != nil {
				return errors.New("必须是数字类型")
			}
		default:
			return errors.New("必须是数字类型")
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return errors.New("必须是布尔类型")
		}
	case "array":
		if _, ok := value.([]interface{}); !ok {
			return errors.New("必须是数组类型")
		}
	case "object":
		if _, ok := value.(map[string]interface{}); !ok {
			return errors.New("必须是对象类型")
		}
	}

	return nil
}

// buildDynamicEntityResponse 构建动态实体响应
func (svc *Service) buildDynamicEntityResponse(entity *model.SysDynamicEntities) (*resp.DynamicEntityResponse, error) {
	response := &resp.DynamicEntityResponse{
		Id:         entity.Id,
		EntityType: entity.EntityType,
		Status:     entity.Status,
		CreateBy:   entity.CreateBy,
		CreateTime: entity.CreateTime.Format(time.RFC3339),
		UpdateBy:   entity.UpdateBy,
		UpdateTime: entity.UpdateTime.Format(time.RFC3339),
	}

	// 解析属性JSON
	var attributes map[string]interface{}
	if err := json.Unmarshal([]byte(entity.Attributes), &attributes); err != nil {
		return nil, fmt.Errorf("解析属性数据失败: %v", err)
	}
	response.Attributes = attributes

	return response, nil
}
