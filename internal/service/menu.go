package service

import (
	"errors"
	"medlink/internal/model"
	"medlink/pkg/app/req"
	"medlink/pkg/app/resp"
	"medlink/pkg/tools"
	"medlink/pkg/tools/convert"
	"strconv"
	"strings"
	"time"
)

func (svc *Service) buildMenuResponse(row *model.SysMenu) *resp.MenuResponse {
	return &resp.MenuResponse{
		CreateTime: tools.Time2String(row.CreateTime),
		Remark:     row.Remark,
		MenuId:     row.MenuId,
		MenuName:   row.MenuName,
		ParentId:   row.ParentId,
		OrderNum:   row.OrderNum,
		Path:       row.Path,
		Component:  row.Component,
		Query:      row.Query,
		RouteName:  row.RouteName,
		IsFrame:    strconv.Itoa(row.IsFrame),
		IsCache:    strconv.Itoa(row.IsCache),
		MenuType:   row.MenuType,
		Visible:    row.Visible,
		Status:     row.Status,
		Perms:      row.Perms,
		Icon:       row.Icon,
	}
}

func (svc *Service) BuildMenuTree(menus []*resp.MenuResponse) []*resp.TreeResponse {
	if len(menus) == 0 {
		return nil
	}
	tree := []*resp.TreeResponse{}
	for _, menu := range menus {
		buf := resp.TreeResponse{
			Id:       menu.MenuId,
			Pid:      menu.ParentId,
			Label:    menu.MenuName,
			Disabled: menu.Status == "1",
			Children: nil,
		}
		tree = append(tree, &buf)
	}
	return tools.BuildSliceToTree(tree, 0)
}

func (svc *Service) GetMenuList(param *req.MenuListRequest) ([]*resp.MenuResponse, error) {
	fields := model.SysMenu{}.GetFields()
	menus, err := svc.dao.GetMenuList(param, fields)
	if err != nil {
		return nil, err
	}
	res := make([]*resp.MenuResponse, 0, len(menus))
	for _, menu := range menus {
		res = append(res, svc.buildMenuResponse(menu))
	}
	return res, err
}

func (svc *Service) GetMenuByMenuId(id uint64) (*resp.MenuResponse, error) {
	fields := model.SysMenu{}.GetFields()
	row, err := svc.dao.GetMenuById(id, fields)
	if err != nil {
		return nil, err
	}
	return svc.buildMenuResponse(row), nil
}

func (svc *Service) GetMenuIdsByRoleId(roleId uint64) []uint64 {
	menuList, _ := svc.dao.GetRoleMenusByRoleIds([]uint64{roleId})
	menuIds := make([]uint64, 0, len(menuList))
	for i := range menuList[roleId] {
		menuIds = append(menuIds, menuList[roleId][i].MenuId)
	}
	return menuIds
}

func (svc *Service) CreateMenu(param *req.CreateMenuRequest) error {
	if svc.CheckMenuNameUnique(param.MenuName) {
		return errors.New("新增菜单'" + param.MenuName + "'失败，菜单名称已存在")
	}
	if param.IsFrame == "0" && !strings.HasPrefix(param.Path, "http") {
		return errors.New("新增菜单'" + param.MenuName + "'失败，外链必须以http(s)://开头")
	}
	if err := svc.dao.CreateMenu(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateMenu(param *req.UpdateMenuRequest) error {
	fields := model.SysMenu{}.GetFields()
	existByMenu, err := svc.dao.GetMenuById(param.MenuId, fields)
	if err != nil {
		return err
	}
	if existByMenu.MenuId == 0 {
		return errors.New("菜单不存在")
	}

	values := map[string]interface{}{}
	if param.MenuName != "" && param.MenuName != existByMenu.MenuName {
		if svc.CheckMenuNameUnique(param.MenuName) {
			return errors.New("修改菜单'" + param.MenuName + "'失败，菜单名称已存在")
		}
		values["menu_name"] = param.MenuName
	}

	if param.ParentId != "" {
		parentId := convert.StrTo(param.ParentId).MustUInt64()
		if parentId != existByMenu.ParentId && existByMenu.MenuId != parentId {
			values["parent_id"] = param.ParentId
		}
	}
	if param.OrderNum != "" {
		orderNum := convert.StrTo(param.OrderNum).MustInt()
		if orderNum != existByMenu.OrderNum {
			values["order_num"] = orderNum
		}
	}
	if param.Path != "" && param.Path != existByMenu.Path {
		values["path"] = param.Path
	}
	if param.Component != "" && param.Component != existByMenu.Component {
		values["component"] = param.Component
	}
	if param.Query != "" && param.Query != existByMenu.Query {
		values["query"] = param.Query
	}
	if param.RouteName != "" && param.RouteName != existByMenu.RouteName {
		values["route_name"] = param.RouteName
	}
	if param.IsFrame != "" {
		isFrame := convert.StrTo(param.IsFrame).MustInt()
		if isFrame != existByMenu.IsFrame {
			if isFrame == 0 && !strings.HasPrefix(param.Path, "http") {
				return errors.New("修改菜单'" + param.MenuName + "'失败，外链必须以http(s)://开头")
			}
			values["is_frame"] = isFrame
		}
	}
	if param.IsCache != "" {
		isCache := convert.StrTo(param.IsCache).MustInt()
		if isCache != existByMenu.IsCache {
			values["is_cache"] = isCache
		}
	}
	if param.MenuType != "" && param.MenuType != existByMenu.MenuType {
		values["menu_type"] = param.MenuType
	}
	if param.Visible != "" && param.Visible != existByMenu.Visible {
		values["visible"] = param.Visible
	}
	if param.Status != "" {
		status := "0"
		if param.Status == "1" {
			status = "1"
		}
		values["status"] = status
	}
	if param.Icon != "" && param.Icon != existByMenu.Icon {
		values["icon"] = param.Icon
	}
	if param.Perms != "" && param.Perms != existByMenu.Perms {
		values["perms"] = param.Perms
	}
	if param.Remark != "" {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateMenu(existByMenu.MenuId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckMenuNameUnique(menuName string) bool {
	row, err := svc.dao.GetMenuByMenuName(menuName, []string{"menu_id"})
	if err != nil {
		return false
	}
	return row.MenuId > 0
}

func (svc *Service) DeleteMenu(id uint64) error {
	if svc.HasChildByMenuId(id) {
		return errors.New("存在子菜单,不允许删除")
	}
	if svc.CheckMenuExistRole(id) {
		return errors.New("菜单已分配,不允许删除")
	}
	return svc.dao.DeleteMenu(id)
}

func (svc *Service) HasChildByMenuId(id uint64) bool {
	parent, err := svc.dao.GetMenuListByParentId(id, []string{"menu_id"})
	if err != nil {
		return false
	}
	return len(parent) > 0
}

func (svc *Service) CheckMenuExistRole(id uint64) bool {
	roleIds, err := svc.dao.GetRoleIdsByMenuId(id)
	if err != nil {
		return false
	}
	return len(roleIds) > 0
}
