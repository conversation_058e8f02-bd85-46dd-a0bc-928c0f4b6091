package service

import (
	"medlink/internal/model"
	"medlink/pkg/app/resp"
	"medlink/pkg/tools"
	"strings"
)

// getRegularRouters 获取普通菜单路由
func (svc *Service) GetRegularRouters(userId uint64) ([]*resp.TreeMenuResponse, error) {
	fields := (model.SysMenu{}).GetFields()
	var rows []*model.SysMenu
	var err error
	if userId == 1 {
		rows, err = svc.dao.GetMenus([]string{"M", "C"}, fields)
		if err != nil {
			return nil, err
		}
	} else {
		rows, err = svc.dao.GetMenusByUserId(userId, []string{"M", "C"}, fields)
		if err != nil {
			return nil, err
		}
	}

	// 去重处理：使用 map 去除重复的菜单
	uniqueMenus := make(map[uint64]*model.SysMenu)
	for _, menu := range rows {
		uniqueMenus[menu.MenuId] = menu
	}

	// 将去重后的菜单转换为切片
	deduplicatedRows := make([]*model.SysMenu, 0, len(uniqueMenus))
	for _, menu := range uniqueMenus {
		deduplicatedRows = append(deduplicatedRows, menu)
	}

	// OrderNum排序
	deduplicatedRows = tools.SortByField(deduplicatedRows, "OrderNum")
	return svc.buildMenuTree(deduplicatedRows), nil
}

// buildMenuTree 构建菜单树
func (svc *Service) buildMenuTree(rows []*model.SysMenu) []*resp.TreeMenuResponse {
	arr := make([]*resp.TreeMenuResponse, 0)
	childrenMap := svc.InitChildMap(rows)
	for _, menu := range rows {
		if menu.ParentId == 0 {
			newMenu := &resp.TreeMenuResponse{
				MenuId:     menu.MenuId,
				Name:       getRouteName(menu),
				Path:       getRouterPath(menu),
				Hidden:     menu.Visible == "1",
				Redirect:   "noRedirect",
				Component:  getComponent(menu),
				AlwaysShow: true,
				Meta: &resp.TreeMetaResponse{
					Title:   menu.MenuName,
					Icon:    menu.Icon,
					NoCache: menu.IsCache == 1,
					Link:    menu.Path,
				},
			}
			svc.FillAllChildren(newMenu, childrenMap)
			arr = append(arr, newMenu)
		}
	}
	return arr
}

// 生成菜单=====================================================================================================
func (svc *Service) InitChildMap(menus []*model.SysMenu) map[uint64][]*resp.TreeMenuResponse {
	childrenMap := make(map[uint64][]*resp.TreeMenuResponse)
	for i := range menus {
		if menus[i].MenuType == "F" { //忽略按钮
			continue
		}
		//每个menu都预设子菜单项
		childrenMap[menus[i].MenuId] = make([]*resp.TreeMenuResponse, 0)
	}

	for i := range menus {
		if menus[i].MenuType == "F" { //忽略按钮
			continue
		}
		link := ""
		if strings.HasPrefix(menus[i].Path, "http") {
			link = menus[i].Path
		}
		//组织父子关系
		buf := resp.TreeMenuResponse{
			MenuId:    menus[i].MenuId,
			Name:      getRouteName(menus[i]),
			Path:      getRouterPath(menus[i]),
			Hidden:    menus[i].Visible == "1",
			Component: getComponent(menus[i]),
			Meta: &resp.TreeMetaResponse{
				Title:   menus[i].MenuName,
				Icon:    menus[i].Icon,
				NoCache: menus[i].IsCache == 1,
				Link:    link,
			},
		}
		pid := menus[i].ParentId
		childrenMap[pid] = append(childrenMap[pid], &buf)
	}
	return childrenMap
}

func (svc *Service) FillAllChildren(m *resp.TreeMenuResponse, childrenMap map[uint64][]*resp.TreeMenuResponse) {
	children := childrenMap[m.MenuId]
	if children == nil || len(children) <= 0 {
		return
	}

	// 初始化子菜单数组（只初始化一次）
	m.Children = make([]*resp.TreeChildrenResponse, 0, len(children))

	// 遍历所有子菜单
	for _, child := range children {
		// 创建子菜单项
		childItem := &resp.TreeChildrenResponse{
			MenuId:    child.MenuId,
			Name:      child.Name,
			Path:      child.Path,
			Hidden:    child.Hidden,
			Component: child.Component,
			Meta:      child.Meta,
		}

		// 递归处理子菜单的子菜单
		svc.fillChildrenRecursive(childItem, childrenMap)

		// 添加子菜单到当前菜单的 Children 数组
		m.Children = append(m.Children, childItem)
	}

	// 设置 ChildrenMap（用于其他用途）
	m.ChildrenMap = children
}

// fillChildrenRecursive 递归填充子菜单的子菜单
func (svc *Service) fillChildrenRecursive(item *resp.TreeChildrenResponse, childrenMap map[uint64][]*resp.TreeMenuResponse) {
	children := childrenMap[item.MenuId]
	if children == nil || len(children) <= 0 {
		return
	}

	// 初始化子菜单数组
	item.Children = make([]*resp.TreeChildrenResponse, 0, len(children))

	// 遍历所有子菜单
	for _, child := range children {
		// 创建子菜单项
		childItem := &resp.TreeChildrenResponse{
			MenuId:    child.MenuId,
			Name:      child.Name,
			Path:      child.Path,
			Hidden:    child.Hidden,
			Component: child.Component,
			Meta:      child.Meta,
		}

		// 递归处理更深层的子菜单
		svc.fillChildrenRecursive(childItem, childrenMap)

		// 添加到当前项的子菜单数组
		item.Children = append(item.Children, childItem)
	}
}

func getRouteName(m *model.SysMenu) string {
	if isMenuFrame(m) {
		return ""
	}
	var str string
	if m.RouteName != "" {
		str = m.RouteName
	} else {
		str = m.Path
	}
	return strings.Title(str)
}

func getRouterPath(m *model.SysMenu) string {
	routerPath := m.Path
	// 内链打开外网方式
	if m.ParentId != 0 && isInnerLink(m) {
		routerPath = innerLinkReplaceEach(m.Path)
	} else if m.ParentId == 0 && m.MenuType == "M" && m.IsFrame == 1 {
		routerPath = "/" + m.Path
	} else if isMenuFrame(m) {
		routerPath = "/"
	}
	return routerPath
}

func isInnerLink(m *model.SysMenu) bool {
	return m.IsFrame == 1 && strings.HasPrefix(m.Path, "http")
}

func innerLinkReplaceEach(path string) string {
	return strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(path, "http://", ""), "https://", ""), "www.", ""), ":", "/")
}

func isMenuFrame(m *model.SysMenu) bool {
	return m.ParentId == 0 && m.MenuType == "M" && m.IsFrame == 0
}

func isParentView(m *model.SysMenu) bool {
	return m.ParentId != 0 && m.MenuType == "M"
}

func getComponent(m *model.SysMenu) string {
	component := "Layout"
	if m.Component != "" && !isMenuFrame(m) {
		component = m.Component
	} else if m.Component == "" && m.ParentId != 0 && isInnerLink(m) {
		component = "InnerLink"
	} else if m.Component == "" && isParentView(m) {
		component = "ParentView"
	}
	return component
}

// =====================================================================================================
