package model

type SysAttributeDefinitions struct {
	Id         uint64 `gorm:"column:id;primaryKey;comment:主键ID，自增长"`
	EntityType string `gorm:"column:entity_type;comment:所属实体类型，如product/user/article等，用于区分不同业务实体"`
	AttrCode   string `gorm:"column:attr_code;comment:属性代码，全系统唯一标识符，如title/price/gallery等，建议使用snake_case命名"`
	AttrName   string `gorm:"column:attr_name;comment:属性显示名称，用于界面展示，如"产品标题"/"价格"等"`
	FieldType  string `gorm:"column:field_type;comment:字段显示类型：text-单行文本, number-数字, textarea-多行文本, single_image-单图片, multi_image-多图片上传, single_video-单视频, multi_video-多视频上传, select-下拉单选, multi_select-下拉多选, radio-单选按钮组, checkbox-复选框组, date-日期选择, datetime-日期时间选择, rich_text-富文本编辑器"`
	DataType   string `gorm:"column:data_type;comment:数据存储类型：string-字符串, integer-整数, decimal-小数, boolean-布尔值, array-数组, object-对象"`
	IsRequired int    `gorm:"column:is_required;comment:是否必填字段：0-非必填，1-必填"`
	Config     string `gorm:"column:config;comment:字段类型的专用配置，JSON格式存储，不同field_type有不同的配置结构"`
	SortOrder  int    `gorm:"column:sort_order;comment:字段显示顺序，数字越小排序越靠前"`
}

func (SysAttributeDefinitions) TableName() string {
	return "sys_attribute_definitions"
}

func (SysAttributeDefinitions) GetFields() []string {
	return []string{
		"id",
		"entity_type",
		"attr_code",
		"attr_name",
		"field_type",
		"data_type",
		"is_required",
		"config",
		"sort_order",
	}
}
