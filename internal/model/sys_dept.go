package model

import (
	"time"
)

type SysDept struct {
	DeptId     uint64    `gorm:"column:dept_id;primaryKey;comment:部门id"`
	ParentId   uint64    `gorm:"column:parent_id;comment:父部门id"`
	Ances<PERSON>  string    `gorm:"column:ancestors;comment:祖级列表"`
	DeptName   string    `gorm:"column:dept_name;comment:部门名称"`
	OrderNum   int       `gorm:"column:order_num;comment:显示顺序"`
	Leader     string    `gorm:"column:leader;comment:负责人"`
	Phone      string    `gorm:"column:phone;comment:联系电话"`
	Email      string    `gorm:"column:email;comment:邮箱"`
	Status     string    `gorm:"column:status;comment:部门状态（0正常 1停用）"`
	DelFlag    string    `gorm:"column:del_flag;comment:删除标志（0代表存在 2代表删除）"`
	CreateBy   string    `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
}

func (SysDept) TableName() string {
	return "sys_dept"
}

func (SysDept) GetFields() []string {
	return []string{
		"dept_id",
		"parent_id",
		"ancestors",
		"dept_name",
		"order_num",
		"leader",
		"phone",
		"email",
		"status",
		"del_flag",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
	}
}
