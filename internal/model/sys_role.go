package model

import (
	"time"
)

type SysRole struct {
	RoleId            uint64    `gorm:"column:role_id;primaryKey;comment:角色ID"`
	RoleName          string    `gorm:"column:role_name;comment:角色名称"`
	RoleKey           string    `gorm:"column:role_key;comment:角色权限字符串"`
	RoleSort          int       `gorm:"column:role_sort;comment:显示顺序"`
	DataScope         string    `gorm:"column:data_scope;comment:数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）"`
	MenuCheckStrictly int       `gorm:"column:menu_check_strictly;comment:菜单树选择项是否关联显示"`
	DeptCheckStrictly int       `gorm:"column:dept_check_strictly;comment:部门树选择项是否关联显示"`
	Status            string    `gorm:"column:status;comment:角色状态（0正常 1停用）"`
	DelFlag           string    `gorm:"column:del_flag;comment:删除标志（0代表存在 2代表删除）"`
	CreateBy          string    `gorm:"column:create_by;comment:创建者"`
	CreateTime        time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy          string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime        time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark            string    `gorm:"column:remark;comment:备注"`
}

func (SysRole) TableName() string {
	return "sys_role"
}

func (SysRole) GetFields() []string {
	return []string{
		"role_id",
		"role_name",
		"role_key",
		"role_sort",
		"data_scope",
		"menu_check_strictly",
		"dept_check_strictly",
		"status",
		"del_flag",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
	}
}
