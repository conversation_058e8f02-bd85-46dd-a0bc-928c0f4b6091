package model

import (
	"time"
)

type SysUserOnline struct {
	SessionId      string    `gorm:"column:sessionId;primaryKey;comment:用户会话id"`
	LoginName      string    `gorm:"column:login_name;comment:登录账号"`
	DeptName       string    `gorm:"column:dept_name;comment:部门名称"`
	Ipaddr         string    `gorm:"column:ipaddr;comment:登录IP地址"`
	LoginLocation  string    `gorm:"column:login_location;comment:登录地点"`
	Browser        string    `gorm:"column:browser;comment:浏览器类型"`
	Os             string    `gorm:"column:os;comment:操作系统"`
	Status         string    `gorm:"column:status;comment:在线状态on_line在线off_line离线"`
	StartTimestamp time.Time `gorm:"column:start_timestamp;comment:session创建时间"`
	LastAccessTime time.Time `gorm:"column:last_access_time;comment:session最后访问时间"`
	ExpireTime     int       `gorm:"column:expire_time;comment:超时时间，单位为分钟"`
}

func (SysUserOnline) TableName() string {
	return "sys_user_online"
}

func (SysUserOnline) GetFields() []string {
	return []string{
		"sessionId",
		"login_name",
		"dept_name",
		"ipaddr",
		"login_location",
		"browser",
		"os",
		"status",
		"start_timestamp",
		"last_access_time",
		"expire_time",
	}
}
