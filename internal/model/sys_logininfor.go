package model

import (
	"time"
)

type SysLogininfor struct {
	InfoId        uint64    `gorm:"column:info_id;primaryKey;comment:访问ID"`
	LoginName     string    `gorm:"column:login_name;comment:登录账号"`
	Ipaddr        string    `gorm:"column:ipaddr;comment:登录IP地址"`
	LoginLocation string    `gorm:"column:login_location;comment:登录地点"`
	Browser       string    `gorm:"column:browser;comment:浏览器类型"`
	Os            string    `gorm:"column:os;comment:操作系统"`
	Status        string    `gorm:"column:status;comment:登录状态（0成功 1失败）"`
	Msg           string    `gorm:"column:msg;comment:提示消息"`
	LoginTime     time.Time `gorm:"column:login_time;comment:访问时间"`
}

func (SysLogininfor) TableName() string {
	return "sys_logininfor"
}

func (SysLogininfor) GetFields() []string {
	return []string{
		"info_id",
		"login_name",
		"ipaddr",
		"login_location",
		"browser",
		"os",
		"status",
		"msg",
		"login_time",
	}
}
