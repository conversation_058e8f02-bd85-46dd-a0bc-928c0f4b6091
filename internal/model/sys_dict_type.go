package model

import (
	"time"
)

type SysDictType struct {
	DictId     uint64    `gorm:"column:dict_id;primaryKey;comment:字典主键"`
	DictName   string    `gorm:"column:dict_name;comment:字典名称"`
	DictType   string    `gorm:"column:dict_type;comment:字典类型"`
	Status     string    `gorm:"column:status;comment:状态（0正常 1停用）"`
	CreateBy   string    `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark     string    `gorm:"column:remark;comment:备注"`
}

func (SysDictType) TableName() string {
	return "sys_dict_type"
}

func (SysDictType) GetFields() []string {
	return []string{
		"dict_id",
		"dict_name",
		"dict_type",
		"status",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
	}
}
