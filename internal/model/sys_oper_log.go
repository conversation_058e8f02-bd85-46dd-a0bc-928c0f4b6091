package model

import (
	"time"
)

type SysOperLog struct {
	OperId        uint64    `gorm:"column:oper_id;primaryKey;comment:日志主键"`
	Title         string    `gorm:"column:title;comment:模块标题"`
	BusinessType  int       `gorm:"column:business_type;comment:业务类型（0其它 1新增 2修改 3删除）"`
	Method        string    `gorm:"column:method;comment:方法名称"`
	RequestMethod string    `gorm:"column:request_method;comment:请求方式"`
	OperatorType  int       `gorm:"column:operator_type;comment:操作类别（0其它 1后台用户 2手机端用户）"`
	OperName      string    `gorm:"column:oper_name;comment:操作人员"`
	DeptName      string    `gorm:"column:dept_name;comment:部门名称"`
	OperUrl       string    `gorm:"column:oper_url;comment:请求URL"`
	OperIp        string    `gorm:"column:oper_ip;comment:主机地址"`
	OperLocation  string    `gorm:"column:oper_location;comment:操作地点"`
	OperParam     string    `gorm:"column:oper_param;comment:请求参数"`
	JsonResult    string    `gorm:"column:json_result;comment:返回参数"`
	Status        int       `gorm:"column:status;comment:操作状态（0正常 1异常）"`
	ErrorMsg      string    `gorm:"column:error_msg;comment:错误消息"`
	OperTime      time.Time `gorm:"column:oper_time;comment:操作时间"`
	CostTime      uint64    `gorm:"column:cost_time;comment:消耗时间"`
}

func (SysOperLog) TableName() string {
	return "sys_oper_log"
}

func (SysOperLog) GetFields() []string {
	return []string{
		"oper_id",
		"title",
		"business_type",
		"method",
		"request_method",
		"operator_type",
		"oper_name",
		"dept_name",
		"oper_url",
		"oper_ip",
		"oper_location",
		"oper_param",
		"json_result",
		"status",
		"error_msg",
		"oper_time",
		"cost_time",
	}
}
