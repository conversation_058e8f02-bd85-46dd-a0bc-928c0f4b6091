package model

import (
	"time"
)

type SysDynamicEntities struct {
	Id         uint64    `gorm:"column:id;primaryKey;comment:"`
	EntityType string    `gorm:"column:entity_type;comment:实体类型，如product/article等"`
	Attributes string    `gorm:"column:attributes;comment:动态属性集合"`
	Status     string    `gorm:"column:status;comment:状态（0正常 1停用）"`
	CreateBy   string    `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
}

func (SysDynamicEntities) TableName() string {
	return "sys_dynamic_entities"
}

func (SysDynamicEntities) GetFields() []string {
	return []string{
		"id",
		"entity_type",
		"attributes",
		"status",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
	}
}
