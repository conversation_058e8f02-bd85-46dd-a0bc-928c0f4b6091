package model

import (
	"time"
)

type SysPost struct {
	PostId     uint64    `gorm:"column:post_id;primaryKey;comment:岗位ID"`
	PostCode   string    `gorm:"column:post_code;comment:岗位编码"`
	PostName   string    `gorm:"column:post_name;comment:岗位名称"`
	PostSort   int       `gorm:"column:post_sort;comment:显示顺序"`
	Status     string    `gorm:"column:status;comment:状态（0正常 1停用）"`
	CreateBy   string    `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark     string    `gorm:"column:remark;comment:备注"`
}

func (SysPost) TableName() string {
	return "sys_post"
}

func (SysPost) GetFields() []string {
	return []string{
		"post_id",
		"post_code",
		"post_name",
		"post_sort",
		"status",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
	}
}
