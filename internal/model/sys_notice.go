package model

import (
	"time"
)

type SysNotice struct {
	NoticeId      uint64    `gorm:"column:notice_id;primaryKey;comment:公告ID"`
	NoticeTitle   string    `gorm:"column:notice_title;comment:公告标题"`
	NoticeType    string    `gorm:"column:notice_type;comment:公告类型（1通知 2公告）"`
	NoticeContent string    `gorm:"column:notice_content;comment:公告内容"`
	Status        string    `gorm:"column:status;comment:公告状态（0正常 1关闭）"`
	CreateBy      string    `gorm:"column:create_by;comment:创建者"`
	CreateTime    time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy      string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime    time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark        string    `gorm:"column:remark;comment:备注"`
}

func (SysNotice) TableName() string {
	return "sys_notice"
}

func (SysNotice) GetFields() []string {
	return []string{
		"notice_id",
		"notice_title",
		"notice_type",
		"notice_content",
		"status",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
	}
}
