#!/bin/bash

# 动态实体API测试脚本
# 使用方法: ./test_dynamic_entities_api.sh

BASE_URL="http://localhost:8080/api"
TOKEN="your_jwt_token_here"  # 请替换为实际的JWT Token

echo "=== 动态实体API测试 ==="
echo "Base URL: $BASE_URL"
echo

# 1. 测试获取表单结构
echo "1. 测试获取表单结构..."
curl -X GET \
  "$BASE_URL/dynamic-entities/form?entity_type=product" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo
echo "---"
echo

# 2. 测试创建动态实体
echo "2. 测试创建动态实体..."
curl -X POST \
  "$BASE_URL/dynamic-entities" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "product",
    "attributes": {
      "title": "测试产品",
      "price": 99.99,
      "description": "这是一个测试产品",
      "category": "phone"
    },
    "status": "0"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo
echo "---"
echo

# 3. 测试验证动态实体数据
echo "3. 测试验证动态实体数据..."
curl -X POST \
  "$BASE_URL/dynamic-entities/validate" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "product",
    "attributes": {
      "title": "测试产品",
      "price": 99.99
    }
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo
echo "---"
echo

# 4. 测试获取动态实体列表
echo "4. 测试获取动态实体列表..."
curl -X GET \
  "$BASE_URL/dynamic-entities/list?entity_type=product&page=1&pageSize=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo
echo "---"
echo

# 5. 测试获取属性定义
echo "5. 测试获取属性定义..."
curl -X GET \
  "$BASE_URL/attribute-definitions/entity?entity_type=product" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo
echo "=== 测试完成 ==="
echo
echo "注意事项："
echo "1. 请确保服务已启动 (http://localhost:8080)"
echo "2. 请替换脚本中的JWT Token"
echo "3. 请确保数据库中有相应的属性定义数据"
echo "4. 如果看到401错误，请检查Token是否有效"
echo "5. 如果看到404错误，请检查路由是否正确注册"
