package app

import "fmt"

func User<PERSON><PERSON><PERSON><PERSON>(userId uint64) string {
	return fmt.Sprintf("ysa:auth:%d", userId)
}

func UserRolesKey(userId uint64) string {
	return fmt.Sprintf("ysa:roles:%d", userId)
}

func UserPermissionsKey(userId uint64) string {
	return fmt.Sprintf("ysa:permissions:%d", userId)
}

func UserSessionKey(userId uint64) string {
	return fmt.Sprintf("sys:session:%d", userId)
}
