package req

type AttrListRequest struct {
	EntityType string `form:"entity_type" label:"实体类型"`
	AttrCode   string `form:"attr_code" label:"属性代码"`
	AttrName   string `form:"attr_name" label:"属性名称"`
	FieldType  string `form:"field_type" label:"字段类型"`
	DataType   string `form:"data_type" label:"数据类型"`
	IsRequired string `form:"is_required" label:"是否必填"`
}

type CreateAttrRequest struct {
	EntityType string `form:"entity_type" binding:"required,max=50" label:"实体类型"`
	AttrCode   string `form:"attr_code" binding:"required,max=100" label:"属性代码"`
	AttrName   string `form:"attr_name" binding:"required,max=100" label:"属性名称"`
	FieldType  string `form:"field_type" binding:"required,max=50" label:"字段类型"`
	DataType   string `form:"data_type" binding:"required,max=20" label:"数据类型"`
	IsRequired string `form:"is_required" binding:"oneof=0 1" label:"是否必填"`
	Config     string `form:"config" binding:"required" label:"字段配置"`
	SortOrder  string `form:"sort_order" binding:"gte=0" label:"排序"`
	Operater   string `form:"operater" label:"操作人"`
}

type UpdateAttrRequest struct {
	Id         uint64 `form:"id" binding:"required,gte=1" label:"属性定义ID"`
	EntityType string `form:"entity_type" binding:"max=50" label:"实体类型"`
	AttrCode   string `form:"attr_code" binding:"max=100" label:"属性代码"`
	AttrName   string `form:"attr_name" binding:"max=100" label:"属性名称"`
	FieldType  string `form:"field_type" binding:"max=50" label:"字段类型"`
	DataType   string `form:"data_type" binding:"max=20" label:"数据类型"`
	IsRequired string `form:"is_required" binding:"omitempty,oneof=0 1" label:"是否必填"`
	Config     string `form:"config" label:"字段配置"`
	SortOrder  string `form:"sort_order" binding:"omitempty,gte=0" label:"排序"`
	Operater   string `form:"operater" label:"操作人"`
}

// 验证配置请求
type ValidateConfigRequest struct {
	FieldType string `form:"field_type" binding:"required" label:"字段类型"`
	Config    string `form:"config" binding:"required" label:"字段配置"`
}
