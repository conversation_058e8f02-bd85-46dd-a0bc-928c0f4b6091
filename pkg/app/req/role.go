package req

type RoleListRequest struct {
	RoleId    uint64 `form:"role_id" label:"角色ID"`
	RoleName  string `form:"roleName" label:"角色名称"`
	Status    string `form:"status" label:"状态"`
	RoleKey   string `form:"roleKey" label:"权限标识"`
	BeginTime string `form:"beginTime" label:"开始时间"`
	EndTime   string `form:"endTime" label:"结束时间"`
}

type CreateRoleRequest struct {
	RoleName          string `form:"roleName" binding:"required" label:"角色名称"`
	RoleKey           string `form:"roleKey" binding:"required" label:"角色权限字符串"`
	RoleSort          string `form:"roleSort" label:"显示顺序"`
	DeptCheckStrictly string `form:"deptCheckStrictly" label:"部门树选择项是否关联显示"`
	DeptIds           string `form:"deptIds"  label:"部门列表"`
	MenuCheckStrictly string `form:"menuCheckStrictly" label:"菜单树选择项是否关联显示"`
	MenuIds           string `form:"menuIds" label:"菜单列表"`
	Status            string `form:"status" binding:"required,oneof=0 1" label:"状态"`
	Remark            string `form:"remark" label:"备注"`
	Operater          string `form:"operater" label:"操作人"`
}

type UpdateRoleRequest struct {
	RoleId            uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	RoleName          string `form:"roleName" label:"角色名称"`
	RoleKey           string `form:"roleKey" label:"角色权限字符串"`
	RoleSort          string `form:"roleSort" label:"显示顺序"`
	DeptCheckStrictly string `form:"deptCheckStrictly" label:"部门树选择项是否关联显示"`
	DeptIds           string `form:"deptIds" label:"部门列表"`
	MenuCheckStrictly string `form:"menuCheckStrictly" label:"菜单树选择项是否关联显示"`
	MenuIds           string `form:"menuIds" label:"菜单列表"`
	Status            string `form:"status" label:"状态"`
	Remark            string `form:"remark" label:"备注"`
	Operater          string `form:"operater" label:"操作人"`
}

type ChangeRoleStatusRequest struct {
	RoleId   uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	Status   string `form:"status" binding:"required,oneof=0 1" label:"状态"`
	Operater string `form:"operater" label:"操作人"`
}

type AuthDataScopeRequest struct {
	RoleId            uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	DataScope         string `form:"dataScope" binding:"required,gte=1" label:"数据范围"`
	DeptCheckStrictly string `form:"deptCheckStrictly" label:"部门树选择项是否关联显示"`
	DeptIds           string `form:"deptIds" label:"部门列表"`
}
