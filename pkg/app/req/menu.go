package req

type MenuListRequest struct {
	MenuId   uint64 `form:"menuId" label:"菜单ID"`
	ParentId uint64 `form:"parentId" label:"父菜单ID"`
	MenuName string `form:"menuName" label:"菜单名称"`
	Visible  string `form:"visible" label:"菜单状态（0显示 1隐藏）"`
	Status   string `form:"status" label:"菜单状态（0正常 1停用）"`
}

type CreateMenuRequest struct {
	MenuName   string `form:"menuName" binding:"required" label:"菜单名称"`
	ParentId   string `form:"parentId" binding:"required" label:"父菜单ID"`
	OrderNum   string `form:"orderNum" binding:"required" label:"显示顺序"`
	Path       string `form:"path" label:"路由地址"`
	Component  string `form:"component" label:"组件路径"`
	Query      string `form:"query" label:"路由参数"`
	RouteName  string `form:"routeName" label:"路由名称"`
	IsFrame    string `form:"isFrame" binding:"required" label:"是否为外链（0是 1否）"`
	IsCache    string `form:"isCache" binding:"required" label:"是否缓存（0缓存 1不缓存）"`
	MenuType   string `form:"menuType" binding:"required" label:"菜单类型"`
	Visible    string `form:"visible" binding:"required,oneof=0 1" label:"显示状态"`
	Status     string `form:"status" binding:"required,oneof=0 1" label:"菜单状态"`
	Perms      string `form:"perms" label:"权限标识"`
	Icon       string `form:"icon" label:"菜单图标"`
	Remark     string `form:"remark" label:"备注"`
	ActiveMenu string `form:"activeMenu" label:"高亮菜单"`
	Operater   string `form:"operater" label:"操作人"`
}

type UpdateMenuRequest struct {
	MenuId     uint64 `form:"menu_id" binding:"required,gte=0" label:"菜单ID"`
	MenuName   string `form:"menuName" label:"菜单名称"`
	ParentId   string `form:"parentId" label:"父菜单ID"`
	OrderNum   string `form:"orderNum" label:"显示顺序"`
	Path       string `form:"path" label:"路由地址"`
	Component  string `form:"component" label:"组件路径"`
	Query      string `form:"query" label:"路由参数"`
	RouteName  string `form:"routeName" label:"路由名称"`
	IsFrame    string `form:"isFrame"  label:"是否为外链（0是 1否）"`
	IsCache    string `form:"isCache"  label:"是否缓存（0缓存 1不缓存）"`
	MenuType   string `form:"menuType"  label:"菜单类型"`
	Visible    string `form:"visible" label:"显示状态"`
	Status     string `form:"status" label:"菜单状态"`
	Perms      string `form:"perms" label:"权限标识"`
	Icon       string `form:"icon" label:"菜单图标"`
	Remark     string `form:"remark" label:"备注"`
	ActiveMenu string `form:"activeMenu" label:"高亮菜单"`
	Operater   string `form:"operater" label:"操作人"`
}
