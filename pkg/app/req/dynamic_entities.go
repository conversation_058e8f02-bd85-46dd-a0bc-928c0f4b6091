package req

// DynamicEntityListRequest 动态实体列表查询请求
type DynamicEntityListRequest struct {
	EntityType string `form:"entity_type" label:"实体类型"`
	Status     string `form:"status" label:"状态"`
	CreateBy   string `form:"create_by" label:"创建者"`
	BeginTime  string `form:"begin_time" label:"开始时间"`
	EndTime    string `form:"end_time" label:"结束时间"`
}

// CreateDynamicEntityRequest 创建动态实体请求
type CreateDynamicEntityRequest struct {
	EntityType string                 `form:"entity_type" binding:"required,max=50" label:"实体类型"`
	Attributes map[string]interface{} `form:"attributes" binding:"required" label:"动态属性"`
	Status     string                 `form:"status" binding:"oneof=0 1" label:"状态"`
	Operater   string                 `form:"operater" label:"操作人"`
}

// UpdateDynamicEntityRequest 更新动态实体请求
type UpdateDynamicEntityRequest struct {
	Id         uint64                 `form:"id" binding:"required,gte=1" label:"实体ID"`
	EntityType string                 `form:"entity_type" binding:"max=50" label:"实体类型"`
	Attributes map[string]interface{} `form:"attributes" label:"动态属性"`
	Status     string                 `form:"status" binding:"oneof=0 1" label:"状态"`
	Operater   string                 `form:"operater" label:"操作人"`
}

// DeleteDynamicEntityRequest 删除动态实体请求
type DeleteDynamicEntityRequest struct {
	Ids      []uint64 `form:"ids" binding:"required,min=1" label:"实体ID列表"`
	Operater string   `form:"operater" label:"操作人"`
}

// GetDynamicEntityFormRequest 获取动态实体表单请求
type GetDynamicEntityFormRequest struct {
	EntityType string `form:"entity_type" binding:"required" label:"实体类型"`
}

// ValidateDynamicEntityRequest 验证动态实体数据请求
type ValidateDynamicEntityRequest struct {
	EntityType string                 `form:"entity_type" binding:"required" label:"实体类型"`
	Attributes map[string]interface{} `form:"attributes" binding:"required" label:"动态属性"`
}
