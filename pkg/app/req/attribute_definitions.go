package req

// AttributeDefinitionListRequest 属性定义列表查询请求
type AttributeDefinitionListRequest struct {
	EntityType string `form:"entity_type" label:"实体类型"`
	AttrCode   string `form:"attr_code" label:"属性代码"`
	AttrName   string `form:"attr_name" label:"属性名称"`
	FieldType  string `form:"field_type" label:"字段类型"`
	DataType   string `form:"data_type" label:"数据类型"`
	IsRequired *int   `form:"is_required" label:"是否必填"`
}

// CreateAttributeDefinitionRequest 创建属性定义请求
type CreateAttributeDefinitionRequest struct {
	EntityType string `form:"entity_type" binding:"required,max=50" label:"实体类型"`
	AttrCode   string `form:"attr_code" binding:"required,max=100" label:"属性代码"`
	AttrName   string `form:"attr_name" binding:"required,max=100" label:"属性名称"`
	FieldType  string `form:"field_type" binding:"required,max=50" label:"字段类型"`
	DataType   string `form:"data_type" binding:"required,max=20" label:"数据类型"`
	IsRequired int    `form:"is_required" binding:"oneof=0 1" label:"是否必填"`
	Config     string `form:"config" binding:"required" label:"字段配置"`
	SortOrder  int    `form:"sort_order" binding:"gte=0" label:"排序"`
	Operater   string `form:"operater" label:"操作人"`
}

// UpdateAttributeDefinitionRequest 更新属性定义请求
type UpdateAttributeDefinitionRequest struct {
	Id         uint64 `form:"id" binding:"required,gte=1" label:"属性定义ID"`
	EntityType string `form:"entity_type" binding:"max=50" label:"实体类型"`
	AttrCode   string `form:"attr_code" binding:"max=100" label:"属性代码"`
	AttrName   string `form:"attr_name" binding:"max=100" label:"属性名称"`
	FieldType  string `form:"field_type" binding:"max=50" label:"字段类型"`
	DataType   string `form:"data_type" binding:"max=20" label:"数据类型"`
	IsRequired *int   `form:"is_required" binding:"omitempty,oneof=0 1" label:"是否必填"`
	Config     string `form:"config" label:"字段配置"`
	SortOrder  *int   `form:"sort_order" binding:"omitempty,gte=0" label:"排序"`
	Operater   string `form:"operater" label:"操作人"`
}

// DeleteAttributeDefinitionRequest 删除属性定义请求
type DeleteAttributeDefinitionRequest struct {
	Ids      []uint64 `form:"ids" binding:"required,min=1" label:"属性定义ID列表"`
	Operater string   `form:"operater" label:"操作人"`
}

// GetAttributeDefinitionsByEntityRequest 根据实体类型获取属性定义请求
type GetAttributeDefinitionsByEntityRequest struct {
	EntityType string `form:"entity_type" binding:"required" label:"实体类型"`
	IsRequired *int   `form:"is_required" label:"是否必填"`
}

// ValidateConfigRequest 验证配置请求
type ValidateConfigRequest struct {
	FieldType string `form:"field_type" binding:"required" label:"字段类型"`
	Config    string `form:"config" binding:"required" label:"字段配置"`
}

// CopyAttributeDefinitionsRequest 复制属性定义请求
type CopyAttributeDefinitionsRequest struct {
	SourceEntityType string `form:"source_entity_type" binding:"required" label:"源实体类型"`
	TargetEntityType string `form:"target_entity_type" binding:"required" label:"目标实体类型"`
	Operater         string `form:"operater" label:"操作人"`
}

// BatchUpdateSortOrderRequest 批量更新排序请求
type BatchUpdateSortOrderRequest struct {
	Items    []SortOrderItem `form:"items" binding:"required,min=1" label:"排序项目"`
	Operater string          `form:"operater" label:"操作人"`
}

// SortOrderItem 排序项目
type SortOrderItem struct {
	Id        uint64 `form:"id" binding:"required,gte=1" label:"属性定义ID"`
	SortOrder int    `form:"sort_order" binding:"gte=0" label:"排序"`
}
