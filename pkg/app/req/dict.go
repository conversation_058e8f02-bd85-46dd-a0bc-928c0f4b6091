package req

type DictDataListRequest struct {
	DictType  string `form:"dictType" label:"字典类型"`
	DictLabel string `form:"dictLabel" label:"字典标签"`
	Status    string `form:"status" label:"状态"`
}

type DictTypeListRequest struct {
	DictName  string `form:"dictName" label:"字典名称"`
	DictType  string `form:"dictType" label:"字典类型"`
	Status    string `form:"status" label:"状态"`
	BeginTime string `form:"begin_time" label:"开始时间"`
	EndTime   string `form:"end_time" label:"结束时间"`
}

type CreateDictTypeRequest struct {
	DictName string `form:"dictName" binding:"required" label:"字典名称"`
	DictType string `form:"dictType" binding:"required" label:"字典类型"`
	Status   string `form:"status" binding:"required,oneof=0 1" label:"状态"`
	Remark   string `form:"remark" label:"备注"`
	Operater string `form:"operater" label:"操作人"`
}

type UpdateDictTypeRequest struct {
	DictId   uint64 `form:"dictId" binding:"required" label:"字典类型ID"`
	DictName string `form:"dictName" label:"字典名称"`
	DictType string `form:"dictType" label:"字典类型"`
	Status   string `form:"status" label:"状态"`
	Remark   string `form:"remark" label:"备注"`
	Operater string `form:"operater" label:"操作人"`
}

type CreateDictDataRequest struct {
	CssClass  string `form:"cssClass"  label:"样式属性（其他样式扩展）"`
	DictLabel string `form:"dictLabel" binding:"required" label:"字典标签"`
	DictSort  string `form:"dictSort" label:"字典排序"`
	DictType  string `form:"dictType" binding:"required" label:"字典类型"`
	DictValue string `form:"dictValue" binding:"required" label:"字典键值"`
	ListClass string `form:"listClass" label:"表格回显样式"`
	Status    string `form:"status" binding:"required,oneof=0 1" label:"状态"`
	Remark    string `form:"remark" label:"备注"`
	Operater  string `form:"operater" label:"操作人"`
}

type UpdateDictDataRequest struct {
	DictCode  uint64 `form:"dictCode" binding:"required" label:"字典数据ID"`
	CssClass  string `form:"cssClass" label:"样式属性"`
	DictLabel string `form:"dictLabel" label:"字典标签"`
	DictSort  string `form:"dictSort" label:"字典排序"`
	DictType  string `form:"dictType" label:"字典类型"`
	DictValue string `form:"dictValue" label:"字典键值"`
	ListClass string `form:"listClass" label:"表格回显样式"`
	Status    string `form:"status"  label:"状态"`
	Remark    string `form:"remark"  label:"备注"`
	Operater  string `form:"operater" label:"操作人"`
}
