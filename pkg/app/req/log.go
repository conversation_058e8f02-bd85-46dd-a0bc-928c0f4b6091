package req

import "medlink/pkg/tools"

// 登录日志信息
type LoginLogInfo struct {
	LoginName  string
	Status     string // "0"成功 "1"失败
	Message    string
	ClientInfo *tools.ClientInfo
}

// OperLogInfo 操作日志信息
type OperLogInfo struct {
	Title         string                 // 模块标题
	BusinessType  int                    // 业务类型
	Method        string                 // 方法名称
	RequestMethod string                 // 请求方式
	OperatorType  int                    // 操作类别
	OperName      string                 // 操作人员
	DeptName      string                 // 部门名称
	OperUrl       string                 // 请求URL
	OperParam     map[string]interface{} // 请求参数
	JsonResult    map[string]interface{} // 返回参数
	Status        int                    // 操作状态
	ErrorMsg      string                 // 错误消息
	CostTime      int64                  // 消耗时间（毫秒）
	ClientInfo    *tools.ClientInfo      // 客户端信息
}
