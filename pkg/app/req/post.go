package req

type PostListRequest struct {
	PostCode string `form:"postCode" label:"岗位编码"`
	PostName string `form:"postName" label:"岗位名称"`
	Status   string `form:"status" label:"状态"`
}

type CreatePostRequest struct {
	PostCode string `form:"post_code" binding:"required" label:"岗位编码"`
	PostName string `form:"post_name" binding:"required" label:"岗位名称"`
	PostSort string `form:"post_sort" label:"显示顺序"`
	Status   string `form:"status" binding:"required,oneof=0 1" label:"状态"`
	Remark   string `form:"remark" label:"备注"`
	Operater string `form:"operater" label:"操作人"`
}

type UpdatePostRequest struct {
	PostId   uint64 `form:"postId" binding:"required,gte=1" label:"岗位id"`
	PostCode string `form:"postCode" label:"岗位编码"`
	PostName string `form:"postName" label:"岗位名称"`
	PostSort string `form:"postSort" label:"显示顺序"`
	Status   string `form:"status" label:"状态"`
	Remark   string `form:"remark" label:"备注"`
	Operater string `form:"operater" label:"操作人"`
}
