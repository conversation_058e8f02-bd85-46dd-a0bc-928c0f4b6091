package req

type UserListRequest struct {
	UserId      uint64 `form:"user_id" label:"用户ID"`
	UserName    string `form:"userName" label:"用户名称"`
	PhoneNumber string `form:"phonenumber" label:"手机号码"`
	Status      string `form:"status" label:"状态"`
	BeginTime   string `form:"beginTime" label:"开始时间"`
	EndTime     string `form:"endTime" label:"结束时间"`
	DeptId      uint64 `form:"deptId" label:"归属部门"`
}

type CreateUserRequest struct {
	NickName    string `form:"nickName" binding:"required,min=2,max=100" label:"用户昵称"`
	DeptId      uint64 `form:"deptId" binding:"required" label:"归属部门"`
	PhoneNumber string `form:"phonenumber" binding:"required,max=11" label:"手机号码"`
	Email       string `form:"email" binding:"required" label:"邮箱"`
	UserName    string `form:"userName" binding:"required,min=2,max=100" label:"用户名称"`
	Avatar      string `form:"avatar" binding:"max=255" label:"头像"`
	Password    string `form:"password" binding:"required,min=6,max=20" label:"用户密码"`
	Sex         string `form:"sex" binding:"required,oneof=0 1" label:"用户性别"`
	Status      string `form:"status" binding:"required,oneof=0 1" label:"状态"`
	PostIds     string `form:"postIds" binding:"required" label:"岗位"`
	RoleIds     string `form:"roleIds" binding:"required" label:"角色"`
	Remark      string `form:"remark" binding:"max=255" label:"备注"`
	Operater    string `form:"operater" label:"操作人"`
}

type UpdateUserRequest struct {
	UserId      uint64 `form:"userId" binding:"required,gte=1" label:"用户id"`
	NickName    string `form:"nickName" label:"用户昵称"`
	DeptId      string `form:"deptId" label:"归属部门"`
	PhoneNumber string `form:"phonenumber" label:"手机号码"`
	Email       string `form:"email" label:"邮箱"`
	UserName    string `form:"userName" label:"用户名称"`
	Avatar      string `form:"avatar" label:"头像"`
	Password    string `form:"password" label:"用户密码"`
	Sex         string `form:"sex" label:"用户性别"`
	Status      string `form:"status" label:"状态"`
	PostIds     string `form:"postIds" label:"岗位"`
	RoleIds     string `form:"roleIds" label:"角色"`
	Remark      string `form:"remark" label:"备注"`
	Operater    string `form:"operater" label:"操作人"`
}

type UpdateUserProfileRequest struct {
	UserId      uint64
	NickName    string `form:"nickName" binding:"required,min=2,max=100"  label:"用户昵称"`
	Email       string `form:"email" binding:"email,max=100"  label:"邮箱"`
	PhoneNumber string `form:"phonenumber" binding:"required,max=11"  label:"手机号码"`
	Sex         string `form:"sex,default=0" binding:"oneof=0 1" label:"用户性别"`
	Operater    string `form:"operater" label:"操作人"`
}

type ResetPwdRequest struct {
	UserId   uint64 `form:"user_id" binding:"required,gte=1" label:"用户id"`
	Password string `form:"password" binding:"required,min=6,max=20" label:"用户密码"`
	Operater string `form:"operater" label:"操作人"`
}

type ChangeUserStatusRequest struct {
	UserId   uint64 `form:"user_id" binding:"required,gte=1" label:"用户id"`
	Status   string `form:"status,default=0" binding:"oneof=0 1" label:"状态"`
	Operater string `form:"operater" label:"操作人"`
}

type ChangeNewPwdRequest struct {
	UserId      uint64 `form:"userId" label:"用户id"`
	OldPassword string `form:"oldPassword" binding:"required,min=6,max=20" label:"旧密码"`
	NewPassword string `form:"newPassword" binding:"required,min=6,max=20" label:"新密码"`
	ConfirmPwd  string `form:"confirmPwd" binding:"required,min=6,max=20,eqfield=NewPassword" label:"确认密码"`
	Operater    string `form:"operater" label:"操作人"`
}

type ChangeAvatarRequest struct {
	UserId   uint64 `form:"userId" label:"用户id"`
	Avatar   string `form:"avatar" binding:"required" label:"头像"`
	Operater string `form:"operater" label:"操作人"`
}

type SelectRoleUserRequest struct {
	UserName    string `form:"userName" binding:"max=100" label:"用户名称"`
	PhoneNumber string `form:"phonenumber" binding:"max=11"  label:"手机号码"`
	RoleId      uint64 `form:"roleId" binding:"gte=1" label:"角色id"`
}

type DeleteAuthUserRequest struct {
	UserId uint64 `form:"userId" binding:"required,gte=1" label:"用户id"`
	RoleId uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
}

type AuthUsersRequest struct {
	RoleId  uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	UserIds string `form:"userIds" binding:"required" label:"用户ids"`
}

type AuthRolesRequest struct {
	UserId  uint64 `form:"userId" binding:"required,gte=1" label:"用户id"`
	RoleIds string `form:"roleIds" binding:"required" label:"角色ids"`
}
