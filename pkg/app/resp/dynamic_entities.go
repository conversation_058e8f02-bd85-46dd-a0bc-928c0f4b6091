package resp

import "medlink/pkg/app"

// DynamicEntityResponse 动态实体响应结构
type DynamicEntityResponse struct {
	Id         uint64                 `json:"id"`
	EntityType string                 `json:"entity_type"`
	Attributes map[string]interface{} `json:"attributes"`
	Status     string                 `json:"status"`
	CreateBy   string                 `json:"create_by"`
	CreateTime string                 `json:"create_time"`
	UpdateBy   string                 `json:"update_by"`
	UpdateTime string                 `json:"update_time"`
}

// DynamicEntityFormResponse 动态实体表单响应结构
type DynamicEntityFormResponse struct {
	EntityType string                         `json:"entity_type"`
	FormSchema []*app.AttributeDefinitionResponse `json:"form_schema"`
}

// DynamicEntityValidationResponse 动态实体验证响应结构
type DynamicEntityValidationResponse struct {
	Valid   bool     `json:"valid"`
	Errors  []string `json:"errors,omitempty"`
	Message string   `json:"message"`
}
