package resp

type RoleResponse struct {
	CreateBy          string   `json:"createBy"`
	CreateTime        string   `json:"createTime"`
	DataScope         string   `json:"dataScope"`
	DelFlag           string   `json:"delFlag"`
	DeptCheckStrictly bool     `json:"deptCheckStrictly"`
	DeptIds           []uint64 `json:"deptIds"`
	MenuCheckStrictly bool     `json:"menuCheckStrictly"`
	MenuIds           []uint64 `json:"menuIds"`
	Permissions       []string `json:"permissions"`
	Remark            string   `json:"remark"`
	RoleId            uint64   `json:"roleId"`
	RoleKey           string   `json:"roleKey"`
	RoleName          string   `json:"roleName"`
	RoleSort          int      `json:"roleSort"`
	Status            string   `json:"status"`
	UpdateBy          string   `json:"updateBy"`
	UpdateTime        string   `json:"updateTime"`
	Flag              bool     `json:"flag"`
}
