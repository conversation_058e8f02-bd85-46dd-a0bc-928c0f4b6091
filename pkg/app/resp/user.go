package resp

import "time"

type UserResponse struct {
	Admin       bool            `json:"admin"`
	Avatar      string          `json:"avatar"`
	UserId      uint64          `json:"userId"`
	DeptId      uint64          `json:"deptId"`
	NickName    string          `json:"nickName"`
	UserName    string          `json:"userName"`
	Email       string          `json:"email"`
	PhoneNumber string          `json:"phonenumber"`
	Sex         string          `json:"sex"`
	Status      string          `json:"status"`
	DelFlag     string          `json:"delFlag"`
	LoginIp     string          `json:"loginIp"`
	LoginDate   string          `json:"loginDate"`
	CreateBy    string          `json:"createBy"`
	CreateTime  string          `json:"createTime"`
	Remark      string          `json:"remark"`
	Dept        *DeptResponse   `json:"dept"`
	PostIds     []uint64        `json:"postIds"`
	Posts       []*PostResponse `json:"posts"`
	RoleIds     []uint64        `json:"roleIds"`
	Roles       []*RoleResponse `json:"roles"`
	TenantType  string          `json:"tenantType"`
	TenantId    uint64          `json:"tenantId"`
}

type SelectUserResponse struct {
	UserId      uint64    `json:"userId"`
	DeptId      uint64    `json:"deptId"`
	UserName    string    `json:"userName"`
	NickName    string    `json:"nickName"`
	Email       string    `json:"email"`
	Phonenumber string    `json:"phonenumber"`
	Status      string    `json:"status"`
	CreateTime  time.Time `json:"createTime"`
}
