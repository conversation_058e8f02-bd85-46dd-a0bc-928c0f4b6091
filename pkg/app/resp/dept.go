package resp

type DeptResponse struct {
	Ancestors  string `json:"ancestors"`
	CreateBy   string `json:"create_by"`
	CreateTime string `json:"create_time"`
	DelFlag    string `json:"delFlag"`
	DeptId     uint64 `json:"deptId"`
	DeptName   string `json:"deptName"`
	Email      string `json:"email"`
	Leader     string `json:"leader"`
	OrderNum   int    `json:"orderNum"`
	ParentId   uint64 `json:"parentId"`
	ParentName string `json:"parentName"`
	Phone      string `json:"phone"`
	Status     string `json:"status"`
	UpdateBy   string `json:"update_by"`
	UpdateTime string `json:"update_time"`
}
