package resp

import "encoding/json"

type AttrResponse struct {
	Id         uint64                 `json:"id"`
	EntityType string                 `json:"entity_type"`
	AttrCode   string                 `json:"attr_code"`
	AttrName   string                 `json:"attr_name"`
	FieldType  string                 `json:"field_type"`
	DataType   string                 `json:"data_type"`
	IsRequired int                    `json:"is_required"`
	Config     map[string]interface{} `json:"config"`
	ConfigRaw  string                 `json:"config_raw,omitempty"` // 原始JSON字符串
	SortOrder  int                    `json:"sort_order"`
	CreateTime string                 `json:"create_time,omitempty"`
	UpdateTime string                 `json:"update_time,omitempty"`
}

// ConfigValidationResponse 配置验证响应
type ConfigValidationResponse struct {
	Valid   bool     `json:"valid"`
	Errors  []string `json:"errors,omitempty"`
	Message string   `json:"message,omitempty"`
}

// SetConfig 设置配置（从JSON字符串解析）
func (a *AttrResponse) SetConfig(configStr string) error {
	if configStr == "" {
		a.Config = make(map[string]interface{})
		return nil
	}

	a.ConfigRaw = configStr
	return json.Unmarshal([]byte(configStr), &a.Config)
}

// GetConfigString 获取配置的JSON字符串
func (a *AttrResponse) GetConfigString() (string, error) {
	if a.Config == nil {
		return "{}", nil
	}

	bytes, err := json.Marshal(a.Config)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// FieldTypeConfigSchema 字段类型配置模式
type FieldTypeConfigSchema struct {
	FieldType   string                 `json:"field_type"`
	Description string                 `json:"description"`
	DataTypes   []string               `json:"data_types"` // 支持的数据类型
	Schema      map[string]interface{} `json:"schema"`     // JSON Schema
	Example     map[string]interface{} `json:"-"`          // 配置示例
	Required    []string               `json:"required"`   // 必需字段
}

// GetFieldTypeSchemas 获取所有字段类型的配置模式
func GetFieldTypeSchemas() []*FieldTypeConfigSchema {
	return []*FieldTypeConfigSchema{
		{
			FieldType:   "text",
			Description: "单行文本输入",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_length":    map[string]interface{}{"type": "integer", "minimum": 1},
					"min_length":    map[string]interface{}{"type": "integer", "minimum": 0},
					"placeholder":   map[string]interface{}{"type": "string"},
					"default_value": map[string]interface{}{"type": "string"},
					"pattern":       map[string]interface{}{"type": "string"},
				},
			},
			Example: map[string]interface{}{
				"max_length":    100,
				"min_length":    0,
				"placeholder":   "请输入标题",
				"default_value": "",
				"pattern":       "",
			},
		},
		{
			FieldType:   "textarea",
			Description: "多行文本输入",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_length":    map[string]interface{}{"type": "integer", "minimum": 1},
					"min_length":    map[string]interface{}{"type": "integer", "minimum": 0},
					"rows":          map[string]interface{}{"type": "integer", "minimum": 1},
					"placeholder":   map[string]interface{}{"type": "string"},
					"default_value": map[string]interface{}{"type": "string"},
				},
			},
			Example: map[string]interface{}{
				"max_length":    1000,
				"min_length":    0,
				"rows":          4,
				"placeholder":   "请输入描述",
				"default_value": "",
			},
		},
		{
			FieldType:   "number",
			Description: "数字输入",
			DataTypes:   []string{"integer", "decimal"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"min":           map[string]interface{}{"type": "number"},
					"max":           map[string]interface{}{"type": "number"},
					"step":          map[string]interface{}{"type": "number"},
					"precision":     map[string]interface{}{"type": "integer", "minimum": 0},
					"default_value": map[string]interface{}{"type": "number"},
					"unit":          map[string]interface{}{"type": "string"},
				},
			},
			Example: map[string]interface{}{
				"min":           0,
				"max":           999999,
				"step":          1,
				"precision":     2,
				"default_value": 0,
				"unit":          "元",
			},
		},
		{
			FieldType:   "single_image",
			Description: "单图片上传",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_size":      map[string]interface{}{"type": "string"},
					"allowed_types": map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
					"dimensions":    map[string]interface{}{"type": "object"},
					"storage":       map[string]interface{}{"type": "object"},
					"thumbnail":     map[string]interface{}{"type": "boolean"},
					"watermark":     map[string]interface{}{"type": "boolean"},
				},
			},
			Example: map[string]interface{}{
				"max_size":      "2MB",
				"allowed_types": []string{"jpg", "png", "webp"},
				"dimensions": map[string]interface{}{
					"min_width":  300,
					"min_height": 300,
					"ratio":      "1:1",
				},
				"storage": map[string]interface{}{
					"driver": "local",
					"path":   "uploads/images",
				},
				"thumbnail": true,
				"watermark": false,
			},
		},
		{
			FieldType:   "multi_image",
			Description: "多图片上传",
			DataTypes:   []string{"array"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_files":     map[string]interface{}{"type": "integer", "minimum": 1},
					"max_size":      map[string]interface{}{"type": "string"},
					"allowed_types": map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
					"dimensions":    map[string]interface{}{"type": "object"},
					"storage":       map[string]interface{}{"type": "object"},
					"thumbnail":     map[string]interface{}{"type": "boolean"},
					"watermark":     map[string]interface{}{"type": "boolean"},
				},
				"required": []string{"max_files"},
			},
			Example: map[string]interface{}{
				"max_files":     10,
				"max_size":      "5MB",
				"allowed_types": []string{"jpg", "png", "webp"},
				"dimensions": map[string]interface{}{
					"min_width":  300,
					"min_height": 300,
					"ratio":      "16:9",
				},
				"storage": map[string]interface{}{
					"driver": "local",
					"path":   "uploads/products/gallery",
				},
				"thumbnail": true,
				"watermark": false,
			},
			Required: []string{"max_files"},
		},
		{
			FieldType:   "single_video",
			Description: "单视频上传",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_size":       map[string]interface{}{"type": "string"},
					"allowed_types":  map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
					"duration_limit": map[string]interface{}{"type": "integer", "minimum": 1},
					"thumbnail":      map[string]interface{}{"type": "boolean"},
					"storage":        map[string]interface{}{"type": "object"},
				},
			},
			Example: map[string]interface{}{
				"max_size":       "50MB",
				"allowed_types":  []string{"mp4", "mov"},
				"duration_limit": 300,
				"thumbnail":      true,
				"storage": map[string]interface{}{
					"driver": "local",
					"path":   "uploads/videos",
				},
			},
		},
		{
			FieldType:   "multi_video",
			Description: "多视频上传",
			DataTypes:   []string{"array"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_files":      map[string]interface{}{"type": "integer", "minimum": 1},
					"max_size":       map[string]interface{}{"type": "string"},
					"allowed_types":  map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
					"duration_limit": map[string]interface{}{"type": "integer", "minimum": 1},
					"thumbnail":      map[string]interface{}{"type": "boolean"},
					"storage":        map[string]interface{}{"type": "object"},
				},
				"required": []string{"max_files"},
			},
			Example: map[string]interface{}{
				"max_files":      3,
				"max_size":       "100MB",
				"allowed_types":  []string{"mp4", "mov"},
				"duration_limit": 300,
				"thumbnail":      true,
				"storage": map[string]interface{}{
					"driver": "s3",
					"bucket": "video-bucket",
				},
			},
			Required: []string{"max_files"},
		},
		{
			FieldType:   "select",
			Description: "下拉单选",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"options":       map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "object"}},
					"default_value": map[string]interface{}{"type": "string"},
					"placeholder":   map[string]interface{}{"type": "string"},
				},
				"required": []string{"options"},
			},
			Example: map[string]interface{}{
				"options": []map[string]interface{}{
					{"value": "s", "label": "小号"},
					{"value": "m", "label": "中号"},
					{"value": "l", "label": "大号"},
				},
				"default_value": "m",
				"placeholder":   "请选择尺寸",
			},
			Required: []string{"options"},
		},
		{
			FieldType:   "multi_select",
			Description: "下拉多选",
			DataTypes:   []string{"array"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"options":        map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "object"}},
					"min_selections": map[string]interface{}{"type": "integer", "minimum": 0},
					"max_selections": map[string]interface{}{"type": "integer", "minimum": 1},
					"placeholder":    map[string]interface{}{"type": "string"},
				},
				"required": []string{"options"},
			},
			Example: map[string]interface{}{
				"options": []map[string]interface{}{
					{"value": "red", "label": "红色"},
					{"value": "blue", "label": "蓝色"},
					{"value": "green", "label": "绿色"},
				},
				"min_selections": 1,
				"max_selections": 2,
				"placeholder":    "请选择颜色",
			},
			Required: []string{"options"},
		},
		{
			FieldType:   "radio",
			Description: "单选按钮组",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"options":       map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "object"}},
					"default_value": map[string]interface{}{"type": "string"},
					"layout":        map[string]interface{}{"type": "string", "enum": []string{"horizontal", "vertical"}},
				},
				"required": []string{"options"},
			},
			Example: map[string]interface{}{
				"options": []map[string]interface{}{
					{"value": "yes", "label": "是"},
					{"value": "no", "label": "否"},
				},
				"default_value": "no",
				"layout":        "horizontal",
			},
			Required: []string{"options"},
		},
		{
			FieldType:   "checkbox",
			Description: "复选框组",
			DataTypes:   []string{"array"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"options":        map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "object"}},
					"min_selections": map[string]interface{}{"type": "integer", "minimum": 0},
					"max_selections": map[string]interface{}{"type": "integer", "minimum": 1},
					"layout":         map[string]interface{}{"type": "string", "enum": []string{"horizontal", "vertical"}},
				},
				"required": []string{"options"},
			},
			Example: map[string]interface{}{
				"options": []map[string]interface{}{
					{"value": "feature1", "label": "功能1"},
					{"value": "feature2", "label": "功能2"},
					{"value": "feature3", "label": "功能3"},
				},
				"min_selections": 0,
				"max_selections": 3,
				"layout":         "vertical",
			},
			Required: []string{"options"},
		},
		{
			FieldType:   "date",
			Description: "日期选择",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"format":        map[string]interface{}{"type": "string"},
					"min_date":      map[string]interface{}{"type": "string"},
					"max_date":      map[string]interface{}{"type": "string"},
					"default_value": map[string]interface{}{"type": "string"},
					"placeholder":   map[string]interface{}{"type": "string"},
				},
			},
			Example: map[string]interface{}{
				"format":        "YYYY-MM-DD",
				"min_date":      "",
				"max_date":      "",
				"default_value": "",
				"placeholder":   "请选择日期",
			},
		},
		{
			FieldType:   "datetime",
			Description: "日期时间选择",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"format":        map[string]interface{}{"type": "string"},
					"min_datetime":  map[string]interface{}{"type": "string"},
					"max_datetime":  map[string]interface{}{"type": "string"},
					"default_value": map[string]interface{}{"type": "string"},
					"placeholder":   map[string]interface{}{"type": "string"},
				},
			},
			Example: map[string]interface{}{
				"format":        "YYYY-MM-DD HH:mm:ss",
				"min_datetime":  "",
				"max_datetime":  "",
				"default_value": "",
				"placeholder":   "请选择日期时间",
			},
		},
		{
			FieldType:   "rich_text",
			Description: "富文本编辑器",
			DataTypes:   []string{"string"},
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"max_length":    map[string]interface{}{"type": "integer", "minimum": 1},
					"min_length":    map[string]interface{}{"type": "integer", "minimum": 0},
					"toolbar":       map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
					"upload_config": map[string]interface{}{"type": "object"},
					"placeholder":   map[string]interface{}{"type": "string"},
				},
			},
			Example: map[string]interface{}{
				"max_length": 10000,
				"min_length": 0,
				"toolbar":    []string{"bold", "italic", "underline", "link", "image", "video"},
				"upload_config": map[string]interface{}{
					"max_size":      "10MB",
					"allowed_types": []string{"jpg", "png", "gif", "mp4"},
				},
				"placeholder": "请输入内容",
			},
		},
	}
}
