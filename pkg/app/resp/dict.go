package resp

type DictTypeResponse struct {
	DictId     uint64 `json:"dictId"`
	DictName   string `json:"dictName"`
	DictType   string `json:"dictType"`
	Status     string `json:"status"`
	CreateBy   string `json:"createBy"`
	CreateTime string `json:"createTime"`
	UpdateBy   string `json:"updateBy"`
	UpdateTime string `json:"updateTime"`
	Remark     string `json:"remark"`
}

type DictDataResponse struct {
	DictCode   uint64 `json:"dictCode"`
	DictSort   int    `json:"dictSort"`
	DictLabel  string `json:"dictLabel"`
	DictValue  string `json:"dictValue"`
	DictType   string `json:"dictType"`
	CssClass   string `json:"cssClass"`
	ListClass  string `json:"listClass"`
	IsDefault  string `json:"isDefault"`
	Status     string `json:"status"`
	CreateBy   string `json:"createBy"`
	CreateTime string `json:"createTime"`
	UpdateBy   string `json:"updateBy"`
	UpdateTime string `json:"updateTime"`
	Remark     string `json:"remark"`
}
