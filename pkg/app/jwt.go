package app

import (
	"medlink/global"
	"time"

	"github.com/dgrijalva/jwt-go"
)

type Claims struct {
	UserId   uint64 `json:"user_id"`
	UserName string `json:"username"`
	jwt.StandardClaims
}

func GetJWTSecret() []byte {
	return []byte(global.App.Config.GetString("JWT.Secret"))
}

func GenerateToken(userid uint64, username string) (string, error) {
	nowTime := time.Now()

	expireTime := nowTime.Add(time.Duration(global.App.Config.GetInt("JWT.Expire")) * time.Minute)
	claims := Claims{
		UserId:   userid,
		UserName: username,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			Issuer:    global.App.Config.GetString("JWT.Issuer"),
		},
	}
	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(GetJWTSecret())

	return token, err
}

func ParseToken(token string) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return GetJWTSecret(), nil
	})
	if err != nil {
		return nil, err
	}
	if tokenClaims != nil {
		claims, ok := tokenClaims.Claims.(*Claims)
		if ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}
