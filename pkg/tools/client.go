package tools

import (
	"net"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// 客户端信息
type ClientInfo struct {
	IP       string
	Location string
	Browser  string
	OS       string
}

// 获取客户端真实IP
func GetClientIP(c *gin.Context) string {
	// 尝试从各种头部获取真实IP
	clientIP := c.GetHeader("X-Forwarded-For")
	if clientIP != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(clientIP, ",")
		if len(ips) > 0 {
			clientIP = strings.TrimSpace(ips[0])
		}
	}

	if clientIP == "" {
		clientIP = c.GetHeader("X-Real-IP")
	}

	if clientIP == "" {
		clientIP = c.GetHeader("X-Original-Forwarded-For")
	}

	if clientIP == "" {
		clientIP = c.GetHeader("CF-Connecting-IP") // Cloudflare
	}

	if clientIP == "" {
		clientIP = c.GetHeader("True-Client-IP") // Akamai
	}

	if clientIP == "" {
		// 从RemoteAddr获取
		ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
		if err != nil {
			clientIP = c.Request.RemoteAddr
		} else {
			clientIP = ip
		}
	}

	// 验证IP格式
	if net.ParseIP(clientIP) == nil {
		clientIP = "unknown"
	}

	return clientIP
}

// 解析User-Agent
func GetUserAgent(c *gin.Context) (browser, os string) {
	userAgent := c.GetHeader("User-Agent")
	if userAgent == "" {
		return "unknown", "unknown"
	}

	// 简单的浏览器检测
	browser = parseBrowser(userAgent)
	os = parseOS(userAgent)

	return browser, os
}

// 解析浏览器信息
func parseBrowser(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	if strings.Contains(userAgent, "chrome") && !strings.Contains(userAgent, "edg") {
		return "Chrome"
	} else if strings.Contains(userAgent, "firefox") {
		return "Firefox"
	} else if strings.Contains(userAgent, "safari") && !strings.Contains(userAgent, "chrome") {
		return "Safari"
	} else if strings.Contains(userAgent, "edg") {
		return "Edge"
	} else if strings.Contains(userAgent, "opera") || strings.Contains(userAgent, "opr") {
		return "Opera"
	} else if strings.Contains(userAgent, "msie") || strings.Contains(userAgent, "trident") {
		return "Internet Explorer"
	}

	return "Unknown Browser"
}

// 解析操作系统信息
func parseOS(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	if strings.Contains(userAgent, "windows nt 10.0") {
		return "Windows 10"
	} else if strings.Contains(userAgent, "windows nt 6.3") {
		return "Windows 8.1"
	} else if strings.Contains(userAgent, "windows nt 6.2") {
		return "Windows 8"
	} else if strings.Contains(userAgent, "windows nt 6.1") {
		return "Windows 7"
	} else if strings.Contains(userAgent, "windows") {
		return "Windows"
	} else if strings.Contains(userAgent, "mac os x") {
		return "macOS"
	} else if strings.Contains(userAgent, "linux") {
		return "Linux"
	} else if strings.Contains(userAgent, "android") {
		return "Android"
	} else if strings.Contains(userAgent, "iphone") || strings.Contains(userAgent, "ipad") {
		return "iOS"
	}

	return "Unknown OS"
}

// 获取完整的客户端信息
func GetClientInfo(c *gin.Context) *ClientInfo {
	ip := GetClientIP(c)
	browser, os := GetUserAgent(c)
	location := GetLocationByIP(ip)

	return &ClientInfo{
		IP:       ip,
		Location: location,
		Browser:  browser,
		OS:       os,
	}
}

// 根据IP获取地理位置（简单实现）
func GetLocationByIP(ip string) string {
	// 这里可以集成第三方IP地理位置服务
	// 比如：高德地图、百度地图、IP138等

	// 简单判断内网IP
	if isPrivateIP(ip) {
		return "内网IP"
	}

	// 这里应该调用真实的IP地理位置服务
	// 暂时返回未知
	return "未知位置"
}

// isPrivateIP 判断是否为内网IP
func isPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查是否为私有IP段
	privateIPBlocks := []*net.IPNet{
		{IP: net.IPv4(10, 0, 0, 0), Mask: net.CIDRMask(8, 32)},     // 10.0.0.0/8
		{IP: net.IPv4(172, 16, 0, 0), Mask: net.CIDRMask(12, 32)},  // **********/12
		{IP: net.IPv4(192, 168, 0, 0), Mask: net.CIDRMask(16, 32)}, // ***********/16
		{IP: net.IPv4(127, 0, 0, 0), Mask: net.CIDRMask(8, 32)},    // *********/8 (localhost)
	}

	for _, block := range privateIPBlocks {
		if block.Contains(parsedIP) {
			return true
		}
	}

	return false
}

// 判断是否为Ajax请求
func IsAjaxRequest(r *http.Request) bool {
	return r.Header.Get("X-Requested-With") == "XMLHttpRequest"
}

// 获取请求方法
func GetRequestMethod(c *gin.Context) string {
	return c.Request.Method
}

// 获取请求URL
func GetRequestURL(c *gin.Context) string {
	return c.Request.URL.String()
}

// 获取来源页面
func GetReferer(c *gin.Context) string {
	return c.GetHeader("Referer")
}
