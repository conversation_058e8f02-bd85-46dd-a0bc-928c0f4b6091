package transaction

import (
	"reflect"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	validator "github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
)

func NewTransaction() {
	// 创建一个翻译器
	uni := ut.New(zh.New())
	// 获取翻译器
	trans, _ := uni.GetTranslator("zh")
	// 获取验证器
	v, ok := binding.Validator.Engine().(*validator.Validate)
	// 注册标签名函数
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		// 获取标签名
		return fld.Tag.Get("label")
	})
	// 如果验证器存在
	if ok {
		// 注册默认翻译
		_ = zh_translations.RegisterDefaultTranslations(v, trans)
	}
}
