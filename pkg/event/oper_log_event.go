package event

import (
	"context"
	"medlink/internal/service"
	"medlink/pkg/app/req"
	"medlink/pkg/tools"
	"time"

	"github.com/gin-gonic/gin"
)

// OperLogEvent 操作日志事件
type OperLogEvent struct {
	Title         string                 `json:"title"`          // 模块标题
	BusinessType  int                    `json:"business_type"`  // 业务类型
	Method        string                 `json:"method"`         // 方法名称
	RequestMethod string                 `json:"request_method"` // 请求方式
	OperatorType  int                    `json:"operator_type"`  // 操作类别
	OperName      string                 `json:"oper_name"`      // 操作人员
	DeptName      string                 `json:"dept_name"`      // 部门名称
	OperUrl       string                 `json:"oper_url"`       // 请求URL
	OperParam     map[string]interface{} `json:"oper_param"`     // 请求参数
	JsonResult    map[string]interface{} `json:"json_result"`    // 返回参数
	Status        int                    `json:"status"`         // 操作状态
	ErrorMsg      string                 `json:"error_msg"`      // 错误消息
	CostTime      int64                  `json:"cost_time"`      // 消耗时间（毫秒）
	ClientInfo    *tools.ClientInfo      `json:"client_info"`    // 客户端信息
}

// Name 实现Event接口
func (e *OperLogEvent) Name() string {
	return "oper_log"
}

// OperLogEventHandler 操作日志事件处理器
type OperLogEventHandler struct{}

// NewOperLogEventHandler 创建操作日志事件处理器
func NewOperLogEventHandler() *OperLogEventHandler {
	return &OperLogEventHandler{}
}

// Handle 处理操作日志事件
func (h *OperLogEventHandler) Handle(ctx context.Context, event Event) error {
	operLogEvent, ok := event.(*OperLogEvent)
	if !ok {
		return nil
	}

	// 动态创建service实例
	svc := service.New(ctx)

	// 构建操作日志信息
	operLogInfo := &req.OperLogInfo{
		Title:         operLogEvent.Title,
		BusinessType:  operLogEvent.BusinessType,
		Method:        operLogEvent.Method,
		RequestMethod: operLogEvent.RequestMethod,
		OperatorType:  operLogEvent.OperatorType,
		OperName:      operLogEvent.OperName,
		DeptName:      operLogEvent.DeptName,
		OperUrl:       operLogEvent.OperUrl,
		OperParam:     operLogEvent.OperParam,
		JsonResult:    operLogEvent.JsonResult,
		Status:        operLogEvent.Status,
		ErrorMsg:      operLogEvent.ErrorMsg,
		CostTime:      operLogEvent.CostTime,
		ClientInfo:    operLogEvent.ClientInfo,
	}

	// 记录操作日志
	return svc.RecordOperLog(operLogInfo)
}

// PublishOperLogEvent 发布操作日志事件
func PublishOperLogEvent(ctx context.Context, c *gin.Context, title string, businessType int, operName, deptName string, result map[string]interface{}, err error, startTime time.Time) {
	clientInfo := tools.GetClientInfo(c)

	// 计算耗时
	costTime := time.Since(startTime).Milliseconds()

	// 请求参数已经在中间件中处理，这里直接使用传入的result作为operParam
	operParam := result

	// 确定操作状态和错误信息
	status := 0 // 成功
	errorMsg := ""
	if err != nil {
		status = 1 // 失败
		errorMsg = err.Error()
	}

	// 分离请求参数和响应数据
	var jsonResult map[string]interface{}
	if responseData, exists := result["response"]; exists {
		if respMap, ok := responseData.(map[string]interface{}); ok {
			jsonResult = respMap
		}
	}

	event := &OperLogEvent{
		Title:         title,
		BusinessType:  businessType,
		Method:        c.HandlerName(),
		RequestMethod: c.Request.Method,
		OperatorType:  1, // 后台用户
		OperName:      operName,
		DeptName:      deptName,
		OperUrl:       c.Request.URL.String(),
		OperParam:     operParam,
		JsonResult:    jsonResult,
		Status:        status,
		ErrorMsg:      errorMsg,
		CostTime:      costTime,
		ClientInfo:    clientInfo,
	}

	// 异步发布事件
	PublishAsync(ctx, event)
}

// 业务类型常量
const (
	BusinessTypeOther   = 0 // 其它
	BusinessTypeInsert  = 1 // 新增
	BusinessTypeUpdate  = 2 // 修改
	BusinessTypeDelete  = 3 // 删除
	BusinessTypeGrant   = 4 // 授权
	BusinessTypeExport  = 5 // 导出
	BusinessTypeImport  = 6 // 导入
	BusinessTypeForce   = 7 // 强退
	BusinessTypeGenCode = 8 // 生成代码
	BusinessTypeClean   = 9 // 清空数据
)

// 操作类别常量
const (
	OperatorTypeOther  = 0 // 其它
	OperatorTypeManage = 1 // 后台用户
	OperatorTypeMobile = 2 // 手机端用户
)

// 操作状态常量
const (
	OperStatusSuccess = 0 // 正常
	OperStatusFail    = 1 // 异常
)
