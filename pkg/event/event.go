package event

import (
	"context"
	"sync"
)

// Event 事件接口
type Event interface {
	Name() string
}

// EventHandler 事件处理器接口
type EventHandler interface {
	Handle(ctx context.Context, event Event) error
}

// EventHandlerFunc 事件处理器函数类型
type EventHandlerFunc func(ctx context.Context, event Event) error

// Handle 实现EventHandler接口
func (f EventHandlerFunc) Handle(ctx context.Context, event Event) error {
	return f(ctx, event)
}

// EventBus 事件总线
type EventBus struct {
	handlers map[string][]EventHandler
	mutex    sync.RWMutex
}

// NewEventBus 创建新的事件总线
func NewEventBus() *EventBus {
	return &EventBus{
		handlers: make(map[string][]EventHandler),
	}
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(eventName string, handler EventHandler) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	eb.handlers[eventName] = append(eb.handlers[eventName], handler)
}

// SubscribeFunc 订阅事件（使用函数）
func (eb *EventBus) SubscribeFunc(eventName string, handlerFunc EventHandlerFunc) {
	eb.Subscribe(eventName, handlerFunc)
}

// Publish 发布事件
func (eb *EventBus) Publish(ctx context.Context, event Event) error {
	eb.mutex.RLock()
	handlers := eb.handlers[event.Name()]
	eb.mutex.RUnlock()

	for _, handler := range handlers {
		if err := handler.Handle(ctx, event); err != nil {
			// 这里可以添加错误处理逻辑
			// 比如记录日志、重试等
			return err
		}
	}

	return nil
}

// PublishAsync 异步发布事件
func (eb *EventBus) PublishAsync(ctx context.Context, event Event) {
	go func() {
		if err := eb.Publish(ctx, event); err != nil {
			// 异步处理错误，可以记录日志
		}
	}()
}

// Unsubscribe 取消订阅
func (eb *EventBus) Unsubscribe(eventName string, handler EventHandler) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	handlers := eb.handlers[eventName]
	for i, h := range handlers {
		if h == handler {
			eb.handlers[eventName] = append(handlers[:i], handlers[i+1:]...)
			break
		}
	}
}

// Clear 清空所有事件处理器
func (eb *EventBus) Clear() {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	eb.handlers = make(map[string][]EventHandler)
}

// GetHandlerCount 获取指定事件的处理器数量
func (eb *EventBus) GetHandlerCount(eventName string) int {
	eb.mutex.RLock()
	defer eb.mutex.RUnlock()

	return len(eb.handlers[eventName])
}

// 全局事件总线实例
var globalEventBus = NewEventBus()

// Subscribe 全局订阅事件
func Subscribe(eventName string, handler EventHandler) {
	globalEventBus.Subscribe(eventName, handler)
}

// SubscribeFunc 全局订阅事件（使用函数）
func SubscribeFunc(eventName string, handlerFunc EventHandlerFunc) {
	globalEventBus.SubscribeFunc(eventName, handlerFunc)
}

// Publish 全局发布事件
func Publish(ctx context.Context, event Event) error {
	return globalEventBus.Publish(ctx, event)
}

// PublishAsync 全局异步发布事件
func PublishAsync(ctx context.Context, event Event) {
	globalEventBus.PublishAsync(ctx, event)
}

// Unsubscribe 全局取消订阅
func Unsubscribe(eventName string, handler EventHandler) {
	globalEventBus.Unsubscribe(eventName, handler)
}

// Clear 清空全局事件总线
func Clear() {
	globalEventBus.Clear()
}

// GetHandlerCount 获取全局事件总线中指定事件的处理器数量
func GetHandlerCount(eventName string) int {
	return globalEventBus.GetHandlerCount(eventName)
}
