package captcha

import (
	"bytes"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"math"
	"math/big"
	"sync"
	"time"
)

// SimpleCaptcha 简单验证码实现
type SimpleCaptcha struct {
	store Store
}

// Store 验证码存储接口
type Store interface {
	Set(id string, value string, expire time.Duration) error
	Get(id string) (string, error)
	Delete(id string) error
}

// MemoryStore 内存存储实现
type MemoryStore struct {
	data map[string]*item
	mu   sync.RWMutex
}

type item struct {
	value  string
	expire time.Time
}

// NewMemoryStore 创建内存存储
func NewMemoryStore() *MemoryStore {
	store := &MemoryStore{
		data: make(map[string]*item),
	}

	// 启动清理协程
	go store.cleanup()

	return store
}

// Set 设置验证码
func (m *MemoryStore) Set(id string, value string, expire time.Duration) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.data[id] = &item{
		value:  value,
		expire: time.Now().Add(expire),
	}

	return nil
}

// Get 获取验证码
func (m *MemoryStore) Get(id string) (string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	item, exists := m.data[id]
	if !exists {
		return "", fmt.Errorf("captcha not found")
	}

	if time.Now().After(item.expire) {
		delete(m.data, id)
		return "", fmt.Errorf("captcha expired")
	}

	return item.value, nil
}

// Delete 删除验证码
func (m *MemoryStore) Delete(id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.data, id)
	return nil
}

// cleanup 清理过期验证码
func (m *MemoryStore) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		m.mu.Lock()
		now := time.Now()
		for id, item := range m.data {
			if now.After(item.expire) {
				delete(m.data, id)
			}
		}
		m.mu.Unlock()
	}
}

// NewSimpleCaptcha 创建简单验证码
func NewSimpleCaptcha(store Store) *SimpleCaptcha {
	return &SimpleCaptcha{
		store: store,
	}
}

// Generate 生成验证码
func (c *SimpleCaptcha) Generate() (id, b64s, answer string, err error) {
	// 生成ID
	id = generateID()

	// 生成验证码答案
	answer = generateCode(4)

	// 生成验证码图片
	img := c.createImage(answer)

	// 转换为base64
	var buf bytes.Buffer
	if err = png.Encode(&buf, img); err != nil {
		return "", "", "", err
	}

	b64s = "data:image/png;base64," + base64.StdEncoding.EncodeToString(buf.Bytes())

	// 存储验证码
	if err = c.store.Set(id, answer, 5*time.Minute); err != nil {
		return "", "", "", err
	}

	return id, b64s, answer, nil
}

// Verify 验证验证码
func (c *SimpleCaptcha) Verify(id, answer string, clear bool) bool {
	storedAnswer, err := c.store.Get(id)
	if err != nil {
		return false
	}

	if clear {
		c.store.Delete(id)
	}

	return storedAnswer == answer
}

// generateID 生成随机ID
func generateID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}

// generateCode 生成验证码
func generateCode(length int) string {
	const charset = "0123456789"
	result := make([]byte, length)

	for i := range result {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result[i] = charset[n.Int64()]
	}

	return string(result)
}

// createImage 创建验证码图片
func (c *SimpleCaptcha) createImage(code string) image.Image {
	width, height := 120, 40
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 填充背景色
	bg := color.RGBA{240, 240, 240, 255}
	draw.Draw(img, img.Bounds(), &image.Uniform{bg}, image.Point{}, draw.Src)

	// 添加干扰线
	c.addNoise(img, width, height)

	// 绘制验证码文字
	c.drawText(img, code, width, height)

	return img
}

// addNoise 添加干扰线
func (c *SimpleCaptcha) addNoise(img *image.RGBA, width, height int) {
	// 添加一些随机点
	for i := 0; i < 100; i++ {
		x, _ := rand.Int(rand.Reader, big.NewInt(int64(width)))
		y, _ := rand.Int(rand.Reader, big.NewInt(int64(height)))
		r, _ := rand.Int(rand.Reader, big.NewInt(256))
		g, _ := rand.Int(rand.Reader, big.NewInt(256))
		b, _ := rand.Int(rand.Reader, big.NewInt(256))

		img.Set(int(x.Int64()), int(y.Int64()), color.RGBA{
			uint8(r.Int64()), uint8(g.Int64()), uint8(b.Int64()), 255,
		})
	}
}

// drawText 绘制文字（简单实现）
func (c *SimpleCaptcha) drawText(img *image.RGBA, text string, width, height int) {
	// 简单的像素字体实现
	charWidth := width / len(text)

	for i, char := range text {
		x := i*charWidth + charWidth/4
		c.drawChar(img, char, x, height/4, charWidth/2, height/2)
	}
}

// drawChar 绘制单个字符（简单的像素实现）
func (c *SimpleCaptcha) drawChar(img *image.RGBA, char rune, x, y, w, h int) {
	// 简单的数字像素图案
	patterns := map[rune][][]int{
		'0': {{1, 1, 1}, {1, 0, 1}, {1, 0, 1}, {1, 0, 1}, {1, 1, 1}},
		'1': {{0, 1, 0}, {1, 1, 0}, {0, 1, 0}, {0, 1, 0}, {1, 1, 1}},
		'2': {{1, 1, 1}, {0, 0, 1}, {1, 1, 1}, {1, 0, 0}, {1, 1, 1}},
		'3': {{1, 1, 1}, {0, 0, 1}, {1, 1, 1}, {0, 0, 1}, {1, 1, 1}},
		'4': {{1, 0, 1}, {1, 0, 1}, {1, 1, 1}, {0, 0, 1}, {0, 0, 1}},
		'5': {{1, 1, 1}, {1, 0, 0}, {1, 1, 1}, {0, 0, 1}, {1, 1, 1}},
		'6': {{1, 1, 1}, {1, 0, 0}, {1, 1, 1}, {1, 0, 1}, {1, 1, 1}},
		'7': {{1, 1, 1}, {0, 0, 1}, {0, 0, 1}, {0, 0, 1}, {0, 0, 1}},
		'8': {{1, 1, 1}, {1, 0, 1}, {1, 1, 1}, {1, 0, 1}, {1, 1, 1}},
		'9': {{1, 1, 1}, {1, 0, 1}, {1, 1, 1}, {0, 0, 1}, {1, 1, 1}},
	}

	pattern, exists := patterns[char]
	if !exists {
		return
	}

	pixelSize := int(math.Min(float64(w/3), float64(h/5)))
	if pixelSize < 1 {
		pixelSize = 1
	}

	for row, line := range pattern {
		for col, pixel := range line {
			if pixel == 1 {
				for dy := 0; dy < pixelSize; dy++ {
					for dx := 0; dx < pixelSize; dx++ {
						px := x + col*pixelSize + dx
						py := y + row*pixelSize + dy
						if px < img.Bounds().Max.X && py < img.Bounds().Max.Y {
							img.Set(px, py, color.RGBA{50, 50, 50, 255})
						}
					}
				}
			}
		}
	}
}
