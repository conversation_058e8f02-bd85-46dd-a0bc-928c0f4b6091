# 动态表单系统

## 概述

这是一个基于Go语言开发的动态表单系统，允许通过配置属性定义来动态生成表单，并将用户填写的数据保存到数据库中。系统支持多种字段类型，具有灵活的配置选项和完整的数据验证功能。

## 核心特性

- ✅ **动态表单生成**: 根据属性定义自动生成表单结构
- ✅ **多种字段类型**: 支持文本、数字、下拉框、多选、日期等多种字段类型
- ✅ **数据验证**: 自动验证必填字段、数据类型和格式
- ✅ **JSON存储**: 动态属性以JSON格式灵活存储
- ✅ **RESTful API**: 提供完整的CRUD操作接口
- ✅ **权限控制**: 集成JWT认证和权限管理
- ✅ **排序支持**: 支持字段排序和批量排序更新

## 数据库表结构

### 1. sys_attribute_definitions (属性定义表)
存储各种实体类型的属性定义，包括字段类型、验证规则、显示配置等。

### 2. sys_dynamic_entities (动态实体表)
存储用户填写的动态表单数据，属性以JSON格式存储。

## 支持的字段类型

| 字段类型 | 数据类型 | 描述 | 配置示例 |
|---------|---------|------|---------|
| text | string | 单行文本输入 | `{"placeholder": "请输入", "max_length": 100}` |
| number | integer/decimal | 数字输入 | `{"min": 0, "max": 100, "step": 0.01}` |
| textarea | string | 多行文本输入 | `{"rows": 4, "placeholder": "请输入"}` |
| select | string | 下拉单选 | `{"options": [{"label": "选项1", "value": "1"}]}` |
| multi_select | array | 下拉多选 | `{"options": [{"label": "选项1", "value": "1"}]}` |
| radio | string/boolean | 单选按钮组 | `{"options": [{"label": "是", "value": true}]}` |
| checkbox | boolean | 复选框 | `{"label": "同意条款"}` |
| date | string | 日期选择 | `{"placeholder": "选择日期"}` |
| datetime | string | 日期时间选择 | `{"placeholder": "选择时间"}` |
| single_image | string | 单图片上传 | `{"accept": "image/*", "max_size": 5242880}` |
| multi_image | array | 多图片上传 | `{"max_count": 10, "max_size": 5242880}` |
| rich_text | string | 富文本编辑器 | `{"toolbar": ["bold", "italic", "link"]}` |

## API 接口

### 属性定义管理
- `GET /attribute-definitions/entity?entity_type={type}` - 获取实体属性定义
- `POST /attribute-definitions` - 创建属性定义
- `PUT /attribute-definitions` - 更新属性定义
- `DELETE /attribute-definitions/{id}` - 删除属性定义

### 动态实体管理
- `GET /dynamic-entities/form?entity_type={type}` - 获取表单结构
- `POST /dynamic-entities` - 创建动态实体
- `PUT /dynamic-entities` - 更新动态实体
- `GET /dynamic-entities/list` - 获取实体列表
- `GET /dynamic-entities/{id}` - 获取实体详情
- `DELETE /dynamic-entities/{id}` - 删除实体
- `POST /dynamic-entities/validate` - 验证实体数据

## 快速开始

### 1. 数据库初始化

```sql
-- 执行示例数据脚本
source docs/example_setup.sql
```

### 2. 启动服务

```bash
go run main.go
```

### 3. 创建属性定义

```bash
curl -X POST http://localhost:8080/api/attribute-definitions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "entity_type": "product",
    "attr_code": "title",
    "attr_name": "产品标题",
    "field_type": "text",
    "data_type": "string",
    "is_required": 1,
    "config": "{\"placeholder\": \"请输入产品标题\", \"max_length\": 100}",
    "sort_order": 1
  }'
```

### 4. 获取表单结构

```bash
curl "http://localhost:8080/api/dynamic-entities/form?entity_type=product" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 创建动态实体

```bash
curl -X POST http://localhost:8080/api/dynamic-entities \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "entity_type": "product",
    "attributes": {
      "title": "iPhone 15 Pro",
      "price": 7999.00,
      "description": "最新款iPhone"
    },
    "status": "0"
  }'
```

## 前端集成

### 1. 获取表单结构并渲染

```javascript
// 获取表单结构
const response = await fetch('/api/dynamic-entities/form?entity_type=product');
const result = await response.json();
const formSchema = result.data.form_schema;

// 根据schema渲染表单
formSchema.forEach(field => {
  const element = createFieldElement(field);
  formContainer.appendChild(element);
});
```

### 2. 提交表单数据

```javascript
const formData = collectFormData();
const response = await fetch('/api/dynamic-entities', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    entity_type: 'product',
    attributes: formData,
    status: '0'
  })
});
```

## 文件结构

```
├── internal/
│   ├── dao/
│   │   ├── sys_attribute_definitions.go    # 属性定义数据访问层
│   │   └── sys_dynamic_entities.go         # 动态实体数据访问层
│   ├── service/
│   │   ├── attribute_definitions.go        # 属性定义业务逻辑层
│   │   └── dynamic_entities.go             # 动态实体业务逻辑层
│   ├── routers/api/
│   │   ├── attribute_definitions.go        # 属性定义控制器
│   │   └── dynamic_entities.go             # 动态实体控制器
│   └── model/
│       ├── sys_attribute_definitions.go    # 属性定义模型
│       └── sys_dynamic_entities.go         # 动态实体模型
├── pkg/app/
│   ├── req/
│   │   ├── attribute_definitions.go        # 属性定义请求结构
│   │   └── dynamic_entities.go             # 动态实体请求结构
│   └── resp/
│       └── dynamic_entities.go             # 动态实体响应结构
└── docs/
    ├── dynamic_form_api.md                 # API文档
    ├── example_setup.sql                   # 示例数据脚本
    └── frontend_example.html               # 前端示例页面
```

## 使用场景

1. **产品管理**: 动态配置产品属性，支持不同类型产品的个性化字段
2. **内容管理**: 灵活定义文章、页面等内容的属性结构
3. **用户资料**: 可扩展的用户信息收集表单
4. **活动报名**: 根据不同活动类型动态生成报名表单
5. **问卷调查**: 快速创建各种类型的调查问卷
6. **配置管理**: 系统配置项的动态管理

## 扩展功能

- [ ] 字段依赖关系 (条件显示)
- [ ] 表单模板功能
- [ ] 数据导入导出
- [ ] 表单版本管理
- [ ] 自定义验证规则
- [ ] 表单预览功能

## 注意事项

1. **权限控制**: 确保正确配置API权限，防止未授权访问
2. **数据验证**: 前后端都需要进行数据验证，确保数据安全
3. **性能优化**: 大量字段时考虑分页加载和懒加载
4. **JSON存储**: 注意JSON字段的查询性能，必要时添加索引
5. **字段配置**: 确保字段配置JSON格式正确，避免解析错误

## 技术栈

- **后端**: Go + Gin + GORM
- **数据库**: MySQL
- **认证**: JWT
- **前端**: 原生JavaScript (可配合任何前端框架)

## 贡献

欢迎提交Issue和Pull Request来改进这个动态表单系统！
