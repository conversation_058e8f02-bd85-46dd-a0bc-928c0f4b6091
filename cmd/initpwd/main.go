package main

import (
	"context"
	"flag"
	"fmt"
	"medlink/global"
	"medlink/internal/dao"
	"medlink/internal/model"
	"medlink/internal/service"
	"medlink/pkg/app"
	"medlink/pkg/app/req"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/shrimps80/go-service-utils/cache"
	"github.com/shrimps80/go-service-utils/config"
	"github.com/shrimps80/go-service-utils/database"
	"github.com/shrimps80/go-service-utils/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 命令行参数
var (
	mode     string
	userType string
	userIds  string
	userName string
	password string
	help     bool
)

// AppConfig 应用配置结构体
type AppConfig struct {
	db    *gorm.DB
	redis *cache.Redis
	cfg   *config.Config
	log   *zap.Logger
}

func init() {
	flag.StringVar(&mode, "mode", "dev", "运行模式 (dev/prod)")
	flag.StringVar(&userType, "type", "", "用户类型: single(单个用户), multiple(多个用户), all(全部用户)")
	flag.StringVar(&userIds, "ids", "", "用户ID列表，用逗号分隔 (仅在type=multiple时使用)")
	flag.StringVar(&userName, "username", "", "用户名 (仅在type=single时使用)")
	flag.StringVar(&password, "password", "123456", "新密码 (默认: 123456)")
	flag.BoolVar(&help, "help", false, "显示帮助信息")
}

func main() {
	flag.Parse()

	if help {
		showHelp()
		return
	}

	// 验证参数
	if err := validateParams(); err != nil {
		fmt.Printf("参数错误: %v\n", err)
		showHelp()
		os.Exit(1)
	}

	// 初始化应用
	app, err := initApp()
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 执行密码重置
	if err := resetPasswords(app); err != nil {
		app.log.Error("密码重置失败", zap.Error(err))
		fmt.Printf("密码重置失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("密码重置完成!")
}

func showHelp() {
	fmt.Println("密码初始化工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/initpwd/main.go [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -mode string")
	fmt.Println("        运行模式 (dev/prod) (默认: dev)")
	fmt.Println("  -type string")
	fmt.Println("        用户类型: single(单个用户), multiple(多个用户), all(全部用户)")
	fmt.Println("  -username string")
	fmt.Println("        用户名 (仅在type=single时使用)")
	fmt.Println("  -ids string")
	fmt.Println("        用户ID列表，用逗号分隔 (仅在type=multiple时使用)")
	fmt.Println("  -password string")
	fmt.Println("        新密码 (默认: 123456)")
	fmt.Println("  -help")
	fmt.Println("        显示帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 重置单个用户密码")
	fmt.Println("  go run cmd/initpwd/main.go -type=single -username=admin -password=newpass123")
	fmt.Println()
	fmt.Println("  # 重置多个用户密码")
	fmt.Println("  go run cmd/initpwd/main.go -type=multiple -ids=1,2,3 -password=newpass123")
	fmt.Println()
	fmt.Println("  # 重置全部用户密码")
	fmt.Println("  go run cmd/initpwd/main.go -type=all -password=newpass123")
}

func validateParams() error {
	if userType == "" {
		return fmt.Errorf("必须指定用户类型 (-type)")
	}

	switch userType {
	case "single":
		if userName == "" {
			return fmt.Errorf("单个用户模式下必须指定用户名 (-username)")
		}
	case "multiple":
		if userIds == "" {
			return fmt.Errorf("多个用户模式下必须指定用户ID列表 (-ids)")
		}
	case "all":
		// 全部用户模式不需要额外参数
	default:
		return fmt.Errorf("无效的用户类型: %s，支持的类型: single, multiple, all", userType)
	}

	if password == "" {
		return fmt.Errorf("密码不能为空")
	}

	return nil
}

func initApp() (*AppConfig, error) {
	app := &AppConfig{}

	// 初始化全局App实例
	global.App = &global.AppConfig{}

	// 初始化配置
	if err := app.initConfig(); err != nil {
		return nil, err
	}

	// 初始化日志
	if err := app.initLogger(); err != nil {
		return nil, err
	}

	// 初始化数据库
	if err := app.initDatabase(); err != nil {
		return nil, err
	}

	// 初始化Redis
	if err := app.initRedis(); err != nil {
		return nil, err
	}

	return app, nil
}

func (app *AppConfig) initConfig() error {
	var err error

	// 获取当前工作目录的绝对路径
	currentDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取当前工作目录失败: %v", err)
	}
	configPath := filepath.Join(currentDir, "configs", mode)

	// 配置选项
	opts := &config.Options{
		ConfigType:  "yaml",
		ConfigName:  "config",
		ConfigPaths: []string{configPath},
	}

	// 创建配置管理器
	app.cfg, err = config.New(opts)
	if err != nil {
		return fmt.Errorf("配置初始化失败: %v", err)
	}

	// 设置全局配置
	global.App.Config = app.cfg

	return nil
}

func (app *AppConfig) initLogger() error {
	logConfig := &logger.Config{
		Filename:   app.cfg.GetString("Log.SavePath") + "/" + app.cfg.GetString("Log.FileName") + app.cfg.GetString("Log.FileExt"),
		MaxSize:    app.cfg.GetInt("Log.MaxSize"),
		MaxBackups: app.cfg.GetInt("Log.MaxBackups"),
		MaxAge:     app.cfg.GetInt("Log.MaxAge"),
		Compress:   app.cfg.GetBool("Log.Compress"),
		Level:      app.cfg.GetString("Log.Level"),
	}

	var err error
	app.log, err = logger.NewLogger(logConfig)
	if err != nil {
		return fmt.Errorf("日志初始化失败: %v", err)
	}

	// 设置全局日志
	global.App.Log = app.log

	return nil
}

func (app *AppConfig) initDatabase() error {
	s := "%s:%s@tcp(%s)/%s?charset=%s&parseTime=%v&loc=Local"
	dsn := fmt.Sprintf(s,
		app.cfg.GetString("Database.Username"),
		app.cfg.GetString("Database.Password"),
		app.cfg.GetString("Database.Host"),
		app.cfg.GetString("Database.DBName"),
		app.cfg.GetString("Database.Charset"),
		app.cfg.GetBool("Database.ParseTime"),
	)

	dbConfig := &database.Config{
		Type:         app.cfg.GetString("Database.DBType"),
		DSN:          dsn,
		MaxIdleConns: app.cfg.GetInt("Database.MaxIdleConns"),
		MaxOpenConns: app.cfg.GetInt("Database.MaxOpenConns"),
		MaxLifetime:  time.Duration(app.cfg.GetInt("Database.MaxLifetime")) * time.Second,
		Debug:        app.cfg.GetBool("Database.debug"),
	}

	var err error
	app.db, err = database.New(dbConfig)
	if err != nil {
		return fmt.Errorf("数据库初始化失败: %v", err)
	}

	// 设置全局数据库
	global.App.DB = app.db

	return nil
}

func (app *AppConfig) initRedis() error {
	redisConfig := &cache.RedisConfig{
		Addrs:      []string{app.cfg.GetString("Redis.Host")},
		Password:   app.cfg.GetString("Redis.Password"),
		DB:         app.cfg.GetInt("Redis.DB"),
		PoolSize:   app.cfg.GetInt("Redis.PoolSize"),
		MaxRetries: 3,
		Timeout:    time.Second * 5,
	}

	var err error
	app.redis, err = cache.NewRedis(redisConfig)
	if err != nil {
		return fmt.Errorf("Redis初始化失败: %v", err)
	}

	// 设置全局Redis
	global.App.Redis = app.redis

	return nil
}

func resetPasswords(app *AppConfig) error {
	ctx := context.Background()
	svc := service.New(ctx)

	switch userType {
	case "single":
		return resetSingleUserPassword(&svc, userName, password)
	case "multiple":
		return resetMultipleUsersPassword(&svc, userIds, password)
	case "all":
		return resetAllUsersPassword(&svc, password)
	default:
		return fmt.Errorf("不支持的用户类型: %s", userType)
	}
}

// resetSingleUserPassword 重置单个用户密码
func resetSingleUserPassword(svc *service.Service, username, newPassword string) error {
	fmt.Printf("正在重置用户 '%s' 的密码...\n", username)

	// 创建DAO实例来获取用户信息
	dao := dao.New(global.App.DB)
	user, err := dao.GetUserByUserName(username, "", []string{"user_id", "user_name"})
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 重置密码
	resetParam := &req.ResetPwdRequest{
		UserId:   user.UserId,
		Password: newPassword,
		Operater: "system",
	}

	if err := svc.ResetPassword(resetParam); err != nil {
		return fmt.Errorf("重置用户 '%s' 密码失败: %v", username, err)
	}

	fmt.Printf("用户 '%s' (ID: %d) 密码重置成功\n", username, user.UserId)
	return nil
}

// resetMultipleUsersPassword 重置多个用户密码
func resetMultipleUsersPassword(svc *service.Service, userIdsStr, newPassword string) error {
	// 解析用户ID列表
	idStrs := strings.Split(userIdsStr, ",")
	userIdList := make([]uint64, 0, len(idStrs))

	for _, idStr := range idStrs {
		idStr = strings.TrimSpace(idStr)
		if idStr == "" {
			continue
		}
		id, err := strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			return fmt.Errorf("无效的用户ID: %s", idStr)
		}
		userIdList = append(userIdList, id)
	}

	if len(userIdList) == 0 {
		return fmt.Errorf("没有有效的用户ID")
	}

	fmt.Printf("正在重置 %d 个用户的密码...\n", len(userIdList))

	successCount := 0
	failedUsers := make([]string, 0)

	for _, userId := range userIdList {
		// 获取用户信息
		dao := dao.New(global.App.DB)
		user, err := dao.GetUserByUserId(userId, "", []string{"user_id", "user_name"})
		if err != nil {
			failedUsers = append(failedUsers, fmt.Sprintf("ID:%d (获取用户信息失败: %v)", userId, err))
			continue
		}

		// 重置密码
		resetParam := &req.ResetPwdRequest{
			UserId:   userId,
			Password: newPassword,
			Operater: "system",
		}

		if err := svc.ResetPassword(resetParam); err != nil {
			failedUsers = append(failedUsers, fmt.Sprintf("%s (ID:%d) (重置失败: %v)", user.UserName, userId, err))
			continue
		}

		fmt.Printf("用户 '%s' (ID: %d) 密码重置成功\n", user.UserName, userId)
		successCount++
	}

	fmt.Printf("\n重置完成: 成功 %d 个，失败 %d 个\n", successCount, len(failedUsers))

	if len(failedUsers) > 0 {
		fmt.Println("失败的用户:")
		for _, failed := range failedUsers {
			fmt.Printf("  - %s\n", failed)
		}
	}

	return nil
}

// resetAllUsersPassword 重置全部用户密码
func resetAllUsersPassword(svc *service.Service, newPassword string) error {
	fmt.Println("正在获取所有用户列表...")

	// 获取所有用户（不分页）
	param := &req.UserListRequest{}
	users, err := getAllUsers(svc, param)
	if err != nil {
		return fmt.Errorf("获取用户列表失败: %v", err)
	}

	if len(users) == 0 {
		fmt.Println("没有找到任何用户")
		return nil
	}

	fmt.Printf("找到 %d 个用户，正在重置密码...\n", len(users))

	successCount := 0
	failedUsers := make([]string, 0)

	for _, user := range users {
		// 重置密码
		resetParam := &req.ResetPwdRequest{
			UserId:   user.UserId,
			Password: newPassword,
			Operater: "system",
		}

		if err := svc.ResetPassword(resetParam); err != nil {
			failedUsers = append(failedUsers, fmt.Sprintf("%s (ID:%d) (重置失败: %v)", user.UserName, user.UserId, err))
			continue
		}

		fmt.Printf("用户 '%s' (ID: %d) 密码重置成功\n", user.UserName, user.UserId)
		successCount++
	}

	fmt.Printf("\n重置完成: 成功 %d 个，失败 %d 个\n", successCount, len(failedUsers))

	if len(failedUsers) > 0 {
		fmt.Println("失败的用户:")
		for _, failed := range failedUsers {
			fmt.Printf("  - %s\n", failed)
		}
	}

	return nil
}

// getAllUsers 获取所有用户（不分页）
func getAllUsers(svc *service.Service, param *req.UserListRequest) ([]*model.SysUser, error) {
	dao := dao.New(global.App.DB)
	fields := model.SysUser{}.GetFields()

	// 创建一个大的分页选项来获取所有用户
	pageOption := &app.PageOption{
		Page:      1,
		PageSize:  10000, // 设置一个足够大的数字
		NeedTotal: false,
	}

	users, _, err := dao.GetUserList(param, fields, pageOption)
	return users, err
}
